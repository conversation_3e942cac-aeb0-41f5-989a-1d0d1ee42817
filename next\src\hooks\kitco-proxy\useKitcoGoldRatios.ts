import { useQuery } from '@tanstack/react-query'
import { fetchGoldRatiosData } from '~/src/services/kitco-proxy/helpers/goldHelpers'
import type { KitcoValue } from '~/src/services/kitco-proxy/types/KitcoValue'

interface KitcoGoldRatiosData {
  xau: KitcoValue | null
  hui: KitcoValue | null
  timestamp: string | null
  xauHuiRatio: number | null
}

/**
 * Custom hook to fetch gold ratios data from Kitco proxy
 * Integrates with React Query for caching and auto-refetching
 */
export function useKitcoGoldRatios() {
  return useQuery<KitcoGoldRatiosData>({
    queryKey: ['kitco-gold-ratios'],
    queryFn: async () => {
      const { xau, hui } = await fetchGoldRatiosData()

      // Calculate XAU/HUI ratio
      let xauHuiRatio: number | null = null
      if (xau && hui && hui.price !== 0) {
        xauHuiRatio = xau.price / hui.price
      }

      return {
        xau,
        hui,
        timestamp: xau?.timestamp || hui?.timestamp || null,
        xauHuiRatio,
      }
    },
    staleTime: 30000, // Data is fresh for 30 seconds
    refetchInterval: 60000, // Refetch every minute
    refetchOnWindowFocus: true,
    retry: 3,
  })
}
