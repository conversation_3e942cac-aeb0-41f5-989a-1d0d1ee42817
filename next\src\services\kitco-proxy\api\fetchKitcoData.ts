import type { KitcoApiRequestParams } from '../types/KitcoApiRequest'
import type { KitcoApiResponse } from '../types/KitcoApiResponse'
import type { KitcoValue } from '../types/KitcoValue'
import { buildApiUrl, isValidSymbol } from '../utils/buildApiUrl'
import { parseXmlResponse } from '../utils/parseXmlResponse'

/**
 * Fetches data from the Kitco proxy API for a given symbol
 *
 * @param params - Request parameters including symbol
 * @returns Promise resolving to parsed API response
 * @throws Error if the request fails or symbol is invalid
 */
export async function fetchKitcoData(
  params: KitcoApiRequestParams,
): Promise<KitcoApiResponse> {
  // Validate input
  if (!params.symbol) {
    throw new Error('Symbol is required')
  }

  if (!isValidSymbol(params.symbol)) {
    throw new Error(`Invalid symbol format: ${params.symbol}`)
  }

  try {
    // Build the API URL
    const url = buildApiUrl(params)

    // Fetch data from the API
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'application/xml, text/xml',
        'User-Agent': 'Kitco-CMS-Next/1.0',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`)
    }

    // Get the XML response as text
    const xmlString = await response.text()

    if (!xmlString) {
      throw new Error('Empty response from API')
    }

    // Parse the XML response
    const parsedResponse = await parseXmlResponse(xmlString)

    if (!parsedResponse.values || parsedResponse.values.length === 0) {
      throw new Error('No data found in API response')
    }

    return parsedResponse
  } catch (error) {
    console.error('Error fetching Kitco data:', error)
    throw new Error(
      `Failed to fetch Kitco data: ${error instanceof Error ? error.message : 'Unknown error'}`,
    )
  }
}

/**
 * Fetches data for a specific symbol and returns only that symbol's data
 *
 * @param symbol - The symbol to fetch (e.g., 'XAU', 'HUI', 'SPX')
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to the specific symbol's data or null if not found
 */
export async function fetchSymbolData(
  symbol: string,
  version?: string,
): Promise<KitcoValue | null> {
  const response = await fetchKitcoData({ symbol, version })

  // Find the specific symbol in the response
  const symbolData = response.values.find(
    (value) => value.symbol.toLowerCase() === symbol.toLowerCase(),
  )

  return symbolData || null
}

/**
 * Fetches data for multiple symbols in a single request
 * Note: The API returns data for multiple symbols regardless of which one is requested
 *
 * @param symbols - Array of symbols to filter from the response
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to an array of symbol data
 */
export async function fetchMultipleSymbolsData(
  symbols: string[],
  version?: string,
): Promise<KitcoValue[]> {
  if (symbols.length === 0) {
    return []
  }

  // Use the first symbol to make the request (API returns all symbols anyway)
  const response = await fetchKitcoData({ symbol: symbols[0], version })

  // Filter the response to only include requested symbols
  const lowerSymbols = symbols.map((s) => s.toLowerCase())
  return response.values.filter((value) =>
    lowerSymbols.includes(value.symbol.toLowerCase()),
  )
}
