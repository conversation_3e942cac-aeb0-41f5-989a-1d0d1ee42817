'use client'

import { useEffect, useState } from 'react'
import { TradingViewSymbol } from '../types/TradingViewSymbol'
import { fuzzySearchSymbols } from '../utils/fuzzySearchSymbols'

interface UseSymbolSearchProps {
  query: string
  limit?: number
  debounceMs?: number // Debounce search
}

interface UseSymbolSearchResult {
  results: TradingViewSymbol[]
  isSearching: boolean
  error: string | null
}

/**
 * Hook for searching TradingView symbols in Elasticsearch
 * @param props Search options
 * @returns Search results and state
 */
export function useSymbolSearch({
  query,
  limit = 10,
  debounceMs = 300,
}: UseSymbolSearchProps): UseSymbolSearchResult {
  const [results, setResults] = useState<TradingViewSymbol[]>([])
  const [isSearching, setIsSearching] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [debouncedQuery, setDebouncedQuery] = useState<string>(query)

  // Debounce query input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [query, debounceMs])

  // Perform search
  useEffect(() => {
    let isMounted = true

    const performSearch = async () => {
      if (!debouncedQuery) {
        if (isMounted) {
          setResults([])
          setError(null)
          setIsSearching(false)
        }
        return
      }

      setIsSearching(true)
      setError(null)

      try {
        // Only search in Elasticsearch
        const searchResults = await fuzzySearchSymbols(debouncedQuery, limit)

        if (isMounted) {
          setResults(searchResults)
        }
      } catch (err) {
        if (isMounted) {
          setError(
            `Error searching symbols: ${err instanceof Error ? err.message : String(err)}`,
          )
        }
      } finally {
        if (isMounted) {
          setIsSearching(false)
        }
      }
    }

    performSearch()

    return () => {
      isMounted = false
    }
  }, [debouncedQuery, limit])

  return { results, isSearching, error }
}
