import type { KitcoValue } from '../types/KitcoValue'
import { isValidXauResponse, parseXauResponse } from '../utils/parseXauResponse'

/**
 * Fetches XAU data specifically using the compact format endpoint
 * URL: https://proxy.kitco.com/getValue?ver=2.0&symbol=XAU&type=xml
 *
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to XAU data or null if not found
 */
export async function fetchXauDataCompact(
  version = '2.0',
): Promise<KitcoValue | null> {
  try {
    // Build the specific XAU URL
    const url = `https://proxy.kitco.com/getValue?ver=${version}&symbol=XAU&type=xml`

    // Fetch data from the API
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'text/plain, text/xml',
        'User-Agent': 'Kitco-CMS-Next/1.0',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`)
    }

    // Get the response as text
    const responseText = await response.text()

    if (!responseText) {
      throw new Error('Empty response from API')
    }

    // Validate response format
    if (!isValidXauResponse(responseText)) {
      throw new Error('Invalid XAU response format')
    }

    // Parse the compact response
    const xauData = parseXauResponse(responseText)

    if (!xauData) {
      throw new Error('Failed to parse XAU data from response')
    }

    return xauData
  } catch (error) {
    console.error('Error fetching XAU data (compact):', error)
    throw new Error(
      `Failed to fetch XAU data: ${error instanceof Error ? error.message : 'Unknown error'}`,
    )
  }
}
