import React, { useMemo } from 'react'

interface DateTimeProps {
  date?: string | Date | null
  timeZone?: string
  timeFormat?: string
  showTimeZone?: boolean
  isEasternTime?: boolean | string | string[]
}

const DateTime: React.FC<DateTimeProps> = ({
  date,
  timeZone = process.env.NEXT_PUBLIC_TIMEZONE || 'America/New_York',
  timeFormat = 'MMM dd, yyyy HH:mm',
  showTimeZone = false,
  isEasternTime, // Default to false for backward compatibility
}) => {
  const formattedTime = useMemo(() => {
    console.log('Input date:', date, 'Type:', typeof date)
    console.log('isEasternTime:', isEasternTime)

    // Handle null/undefined dates
    if (!date) {
      console.error('Date is null or undefined:', date)
      return 'No Date'
    }

    let parsedDate: Date

    if (typeof date === 'string') {
      if (isEasternTime) {
        // For Eastern Time strings, parse as-is without timezone conversion
        parsedDate = new Date(date)
      } else if (
        !date.includes('Z') &&
        !date.includes('+') &&
        !date.match(/T\d{2}:/)
      ) {
        // Handle common date formats and ensure UTC parsing
        let normalizedDate = date.trim()

        // Convert common formats to ISO format
        if (normalizedDate.match(/^\d{2}\/\d{2}\/\d{4}/)) {
          // Format: MM/DD/YYYY or DD/MM/YYYY
          const parts = normalizedDate.split(' ')
          const datePart = parts[0]
          const timePart = parts.slice(1).join(' ')

          // Assume MM/DD/YYYY format and convert to ISO
          const [month, day, year] = datePart.split('/')
          const isoDateString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`

          if (timePart) {
            // Parse time part
            let time24 = timePart
            if (
              timePart.toLowerCase().includes('am') ||
              timePart.toLowerCase().includes('pm')
            ) {
              const isPM = timePart.toLowerCase().includes('pm')
              const timeOnly = timePart.replace(/[ap]m/i, '').trim()
              const [hours, minutes, seconds] = timeOnly.split(':')
              let hour24 = parseInt(hours)

              if (isPM && hour24 !== 12) hour24 += 12
              if (!isPM && hour24 === 12) hour24 = 0

              time24 = `${hour24.toString().padStart(2, '0')}:${minutes}${seconds ? ':' + seconds : ':00'}`
            }
            normalizedDate = `${isoDateString}T${time24}Z`
          } else {
            normalizedDate = `${isoDateString}T00:00:00Z`
          }
        } else {
          // For other string formats, try to append Z to make it UTC
          normalizedDate = normalizedDate.replace(' ', 'T')
          if (!normalizedDate.endsWith('Z')) {
            normalizedDate += 'Z'
          }
        }
        parsedDate = new Date(normalizedDate)
      } else {
        parsedDate = new Date(date)
      }
    } else if (date instanceof Date) {
      parsedDate = date
      // Check if the date is already in Eastern Time by comparing timezone offset
      const estOffset = new Date()
        .toLocaleString('en-US', { timeZone: 'America/New_York' })
        .match(/([+-]\d{4})/)?.[1]
      const dateOffset = -parsedDate.getTimezoneOffset() / 60
      isEasternTime =
        dateOffset === (estOffset ? parseInt(estOffset) / 100 : -5)
    } else {
      console.error('Invalid date type:', typeof date, date)
      return 'Invalid Date Type'
    }

    // Check if parsedDate is valid
    if (!parsedDate || isNaN(parsedDate.getTime())) {
      console.error('Invalid date:', date, 'Parsed as:', parsedDate)
      return 'Invalid Date'
    }

    console.log(
      'Parsed date:',
      parsedDate.toISOString(),
      'Is Eastern Time:',
      isEasternTime,
    )

    // Format the date according to timeFormat and timeZone
    try {
      // Parse the timeFormat to determine what to show
      const showDate =
        timeFormat.toLowerCase().includes('mmm') ||
        timeFormat.toLowerCase().includes('dd') ||
        timeFormat.toLowerCase().includes('yyyy')
      const showTime =
        timeFormat.toLowerCase().includes('hh') ||
        timeFormat.toLowerCase().includes('mm')

      const options: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: isEasternTime ? 'America/New_York' : timeZone,
        hour12: false,
      }

      if (showDate) {
        options.year = 'numeric'
        options.month = 'short'
        options.day = '2-digit'
      }

      if (showTime) {
        options.hour = '2-digit'
        options.minute = '2-digit'
      }

      if (showTimeZone) {
        options.timeZoneName = 'short'
      }

      const formatter = new Intl.DateTimeFormat('en-US', options)
      const formatted = formatter.format(parsedDate)

      console.log(
        'Formatted result:',
        formatted,
        'Using timezone:',
        options.timeZone,
      )
      return formatted
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'Format Error'
    }
  }, [date, timeZone, timeFormat, showTimeZone, isEasternTime])

  return <span>{formattedTime}</span>
}

export default DateTime
