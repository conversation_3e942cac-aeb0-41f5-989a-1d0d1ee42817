import { elasticClient } from '../client/elasticsearch'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Search for symbols in Elasticsearch using a two-phase approach
 * First tries exact match, then falls back to wildcard search if no results
 *
 * @param query Search query for symbol name or description
 * @param limit Maximum number of results to return
 * @returns Array of symbols matching the query
 */
export async function searchSymbolsInElastic(
  query: string,
  limit: number = 10,
): Promise<any[]> {
  try {
    // Phase 1: Try exact match first
    const exactMatchResponse = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: limit,
      query: {
        match: {
          'symbol-fullname': query,
        },
      },
    })

    const exactHits = exactMatchResponse.hits?.hits || []

    // If we got results from exact match, return them
    if (exactHits.length > 0) {
      return exactHits.map((hit) => hit._source)
    }

    // Phase 2: No exact matches, try wildcard search (matching the working curl command)
    const wildcardResponse = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: limit,
      query: {
        wildcard: {
          'symbol-fullname.keyword': {
            value: `*${query}*`,
          },
        },
      },
    })

    let wildcardHits = wildcardResponse.hits?.hits || []

    // If we still have no results, try searching in other fields
    if (wildcardHits.length === 0) {
      const fallbackResponse = await elasticClient.search({
        index: SYMBOLS_INDEX,
        size: limit,
        query: {
          bool: {
            should: [
              {
                wildcard: {
                  symbol: {
                    value: `*${query}*`,
                  },
                },
              },
              {
                match: {
                  description: query,
                },
              },
            ],
          },
        },
      })

      wildcardHits = fallbackResponse.hits?.hits || []
    }

    const results = wildcardHits.map((hit) => hit._source)
    return results
  } catch (error) {
    console.error('Error searching symbols in Elasticsearch:', error)
    return []
  }
}
