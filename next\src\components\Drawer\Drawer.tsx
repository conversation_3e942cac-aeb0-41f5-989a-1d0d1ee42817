import clsx from 'clsx'
import type React from 'react'
import { useState } from 'react'
import { FaRegCircleXmark } from 'react-icons/fa6'
import { MdOutlineDragIndicator } from 'react-icons/md'
import useScreenSize from '~/src/utils/useScreenSize'

/**
 * The Drawer component props
 */
interface DrawerProps {
  // The Drawer is opened or closed
  opened: boolean
  // The title of the Drawer
  title?: string
  // The event emitter when the user close the Drawer
  onClose: () => void
  // The content of the Drawer
  children?: React.ReactNode
}

/**
 * The Drawer component
 * Is a container that can be opened or closed.
 * It can be resized and is responsive.
 *
 * @param opened
 * @param title
 * @param onClose
 * @param children
 * @constructor
 */
const Drawer = ({ opened, title, onClose, children }: DrawerProps) => {
  // The width of the Drawer that can be resized
  const [drawerWidth, setDrawerWidth] = useState(400)

  // The minimum width of the Drawer
  const minWidth = 300

  // Check if the user is on a mobile device
  const { isMobile } = useScreenSize()

  /**
   * The event emitter when the user click on the overlay to close the Drawer
   *
   * @param e
   */
  const handleCloseClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  /**
   * The event emitter when the user click on the resize handle to resize the Drawer
   *
   * @param e
   */
  const handleMouseDown = (e) => {
    e.stopPropagation()
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  /**
   * The event emitter when the user move the mouse to resize the Drawer
   *
   * @param e
   */
  const handleMouseMove = (e) => {
    const maxWidth = window !== null ? window.innerWidth : 1024

    // Check if the new width is between minWidth and the window width (to prevent the Drawer to be too small or too large)
    const newWidth = Math.max(
      minWidth,
      Math.min(maxWidth, maxWidth - e.clientX),
    )

    setDrawerWidth(newWidth)
    e.stopPropagation()
  }

  /**
   * The event emitter when the user release the mouse to stop resizing the Drawer
   */
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  return (
    <>
      {/* The overlay and also close the Drawer */}
      {!isMobile && (
        <div
          className={clsx(
            'fixed inset-0 z-30 w-screen overflow-y-auto bg-gray-900/50',
            'min-h-full',
            'duration-600 transition-opacity ease-in-out',
            opened ? '' : 'hidden',
          )}
          onClick={handleCloseClick}
        ></div>
      )}

      {/* The Drawer */}
      <div
        className={clsx(
          'fixed right-0 top-0 h-screen w-full overflow-y-scroll md:w-1/3',
          'text-normal z-[40] bg-white text-left shadow-2xl',
          'transition-transform duration-300 ease-in-out',
          opened ? 'translate-x-0' : 'translate-x-full',
        )}
        style={{ width: isMobile ? '100%' : `${drawerWidth}px` }}
      >
        <div
          className="align-content-end justify-content-end sticky top-2.5 z-50
          ml-auto flex w-full justify-end pr-2"
        >
          <button
            onClick={onClose}
            type="button"
            className="z-50 inline-flex h-8 w-8 items-center
                    justify-center rounded-lg bg-transparent text-lg text-gray-400 hover:text-gray-900"
          >
            <FaRegCircleXmark width={'1rem'} />
            <span className="sr-only">Close Drawer</span>
          </button>
        </div>
        <div
          className={clsx(
            'sticky left-0 top-0 flex h-full w-3 cursor-col-resize',
            'justify-content-center inset-y-0 select-none items-center bg-gray-200',
            isMobile ? 'hidden' : '',
          )}
          onMouseDown={handleMouseDown}
        >
          <MdOutlineDragIndicator />
        </div>
        <div
          className={clsx(
            'absolute inset-x-0 top-0 h-max overflow-y-auto scroll-smooth p-4',
            'overscroll-contain',
            isMobile ? 'left-0' : 'left-3',
          )}
        >
          {title && (
            <h5
              className="mb-4 inline-flex items-start justify-between text-base
              font-semibold text-gray-500 dark:text-gray-400"
            >
              {title}
            </h5>
          )}
          <div>{children}</div>
        </div>
      </div>
    </>
  )
}

export default Drawer
