export interface TradingViewWidgetProps {
  symbol: string
  onError?: (error: string) => void
  theme?: 'light' | 'dark'
  autosize?: boolean
  height?: number
  width?: number
  interval?: string
  timezone?: string
  style?: '1' | '2' | '3' | '4'
  locale?: string
  toolbar_bg?: string
  enable_publishing?: boolean
  withdateranges?: boolean
  hide_side_toolbar?: boolean
  allow_symbol_change?: boolean
  save_image?: boolean
  show_popup_button?: boolean
  popup_width?: string
  popup_height?: string
  container_id?: string
  largeChartUrl?: string
}

// Add TradingView types for global window object
declare global {
  interface Window {
    TradingView: {
      widget: new (configuration: any) => any
    }
  }
}
