import type { KitcoValue } from './KitcoValue'

/**
 * Represents the complete response from the Kitco proxy API
 */
export interface KitcoApiResponse {
  values: KitcoValue[]
}

/**
 * Raw XML structure as it comes from the API
 */
export interface KitcoRawXmlResponse {
  Values: {
    Value: KitcoRawValue[]
  }
}

/**
 * Raw value structure from XML before transformation
 */
export interface KitcoRawValue {
  Symbol: string[]
  Timestamp: string[]
  Price: string[]
  Change: string[]
  ChangePercentage: string[]
  ChangeUSD?: string[]
  ChangePercentUSD?: string[]
  ChangeTrade?: string[]
  ChangePercentTrade?: string[]
}
