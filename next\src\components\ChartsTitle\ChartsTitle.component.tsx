import Link from 'next/link'
import { useRouter } from 'next/router'
import cs from '~/src/utils/cs'
import { SocialsKitco } from '../socials/socials-kitco.component'

const baseH1 = `uppercase md:text-[48px] text-[32px]`
const baseSubH1 = `uppercase md:text-[48px] md:leading-[58px] text-[32px] leading-[38px]`

const parentCSS = 'text-ktc-date-gray'

export const ChartsTitle = () => {
  const r = useRouter()

  const subTitle = r?.query?.name

  const showTitle = 'Live Charts'

  return (
    <>
      <div className="mb-5 flex justify-between border-b border-ktc-borders">
        <div className="mb-2 block items-center justify-between gap-5 md:flex">
          <div>
            <div className="flex flex-wrap items-center leading-[38px] md:leading-[58px]">
              <h1 className={cs([baseH1, parentCSS])}>{showTitle}</h1>
              <h1 className={cs([baseH1, parentCSS, 'px-1 md:px-2'])}>/</h1>
              <h1 className={cs([baseSubH1, 'text-kitco-black'])}>
                {subTitle}
              </h1>
            </div>
          </div>
        </div>

        <SocialsKitco
          className="mx-3 hidden justify-between md:flex"
          hidePrint={true}
          listAuthorStr={''}
        />
      </div>
    </>
  )
}

const Root: React.FC<{
  children: React.ReactNode
}> = ({ children }) => (
  <div className="mb-5 flex justify-between border-b border-ktc-borders">
    <div className="mb-2 block items-center justify-between gap-5 md:flex">
      <div>
        <div className="flex flex-wrap items-center leading-[38px] md:leading-[58px]">
          {children}
        </div>
      </div>
    </div>

    <SocialsKitco
      className="mx-3 hidden justify-between md:flex"
      hidePrint={true}
      listAuthorStr={''}
    />
  </div>
)

const Title: React.FC<{ href?: string; children: React.ReactNode }> = ({
  href,
  children,
}) => {
  return (
    <>
      {!href ? (
        <h1 className={cs([baseH1, parentCSS, 'text-black'])}>{children}</h1>
      ) : (
        <Link href={href}>
          <h1 className={cs([baseH1, parentCSS])}>{children}</h1>
        </Link>
      )}
    </>
  )
}

const Divider: React.FC = () => (
  <h1 className={cs([baseH1, parentCSS, 'px-1 md:px-2'])}>/</h1>
)

const SubTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <h1 className={cs([baseSubH1, 'text-kitco-black'])}>{children}</h1>
)

export const PageHeader = {
  Root,
  Title,
  Divider,
  SubTitle,
}
