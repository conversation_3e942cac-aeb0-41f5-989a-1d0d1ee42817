import { elasticClient, isElasticAvailable } from '../client/elasticsearch'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Checks if a symbol exists.
 */
export async function symbolExists(symbol: string): Promise<boolean> {
  // Return false if Elasticsearch is not available
  if (!isElasticAvailable() || !elasticClient) {
    console.log(
      `[symbolExists] Elasticsearch not available, skipping check for symbol: ${symbol}`,
    )
    return false
  }

  try {
    console.log(
      `[symbolExists] Searching for symbol: ${symbol} (lowercase: ${symbol.toLowerCase()})`,
    )

    const response = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 0,
      query: {
        term: { symbol: symbol.toLowerCase() },
      },
    })

    const exists =
      response.hits.total && typeof response.hits.total === 'object'
        ? response.hits.total.value > 0
        : (response.hits.total as number) > 0

    console.log(
      `[symbolExists] Result for ${symbol}: ${exists} (total hits: ${JSON.stringify(response.hits.total)})`,
    )
    return exists
  } catch (error) {
    console.error(
      `[symbolExists] Error checking if symbol exists in Elasticsearch for ${symbol}:`,
      error,
    )

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as any).message || ''
      if (
        errorMessage.includes('ECONNREFUSED') ||
        errorMessage.includes('ConnectionError')
      ) {
        console.error(
          `[symbolExists] Elasticsearch connection failed - service may be down`,
        )
      }
    }

    return false
  }
}
