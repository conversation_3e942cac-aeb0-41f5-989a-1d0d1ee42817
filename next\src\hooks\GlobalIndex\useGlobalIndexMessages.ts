import { useCommodity } from '~/src/contexts/CommodityContext'
import type CommodityData from '~/src/types/DataTable/CommodityData'

function useGoldIndexMessages(value: CommodityData[]) {
  const { selectedCommodity } = useCommodity()
  const commodity = selectedCommodity?.commodity || 'Gold'

  // if no data, return empties
  if (!value || value.length === 0) {
    return {
      getQuestionMessage: () => '',
      getExplanationMessage: () => '',
      commodityData: null,
      getWidgetMessage: () => ({ text: '', value: '' }),
    }
  }

  // find the target commodity entry
  const commodityData =
    value.find(
      (item) => item.commodity.toLowerCase() === commodity.toLowerCase(),
    ) || value[0]

  let { totalChange, changeDueToUSD, changeDueToTrade } = commodityData

  // QUICK TEST: uncomment the block below to test a specific scenario
  // const testCommodity = {
  //   commodity: 'Gold',
  //   totalChange: { change: '-$2.50', changeVal: -2.5, percentage: -0.11 },
  //   changeDueToUSD: { change: '+$5.00', changeVal: 5, percentage: 0.22 },
  //   changeDueToTrade: { change: '-$7.50', changeVal: -7.5, percentage: -0.33 },
  // }
  // totalChange = testCommodity.totalChange
  // changeDueToUSD = testCommodity.changeDueToUSD
  // changeDueToTrade = testCommodity.changeDueToTrade
  // commodityData.commodity = testCommodity.commodity

  // helper: maps a number to -1, 0, or +1
  const sign = (v: number) => (v > 0 ? 1 : v < 0 ? -1 : 0)

  // templates for each (USD‑sign, Trade‑sign) combo
  const TEMPLATES: Record<
    string,
    {
      question: (c: CommodityData) => string
      explanation: (c: CommodityData) => string
    }
  > = {
    '1,1': {
      question: (c) =>
        `Did ${c.commodity} really go ${c.totalChange.changeVal > 0 ? 'up' : 'down'} ${c.totalChange.change}?`,
      explanation: (c) =>
        `Yes. The weakened US Dollar was responsible for ${c.changeDueToUSD.change} of that increase.`,
    },
    '1,-1': {
      question: (c) =>
        `Did ${c.commodity} really go ${c.totalChange.changeVal > 0 ? 'up' : 'down'} ${c.totalChange.change}?`,
      explanation: (c) =>
        `No. It actually went down ${c.changeDueToTrade.change} in real terms, but US Dollar weakness makes it appear to have gone the other way.`,
    },
    '-1,1': {
      question: (c) =>
        `Did ${c.commodity} really go ${c.totalChange.changeVal > 0 ? 'up' : 'down'} ${c.totalChange.change}?`,
      explanation: (c) =>
        `No. It actually went up ${c.changeDueToTrade.change} in real terms, but US Dollar strength masks that.`,
    },
    '-1,-1': {
      question: (c) =>
        `Did ${c.commodity} really go ${c.totalChange.changeVal > 0 ? 'up' : 'down'} ${c.totalChange.change}?`,
      explanation: (c) =>
        `Yes. US Dollar strength was responsible for ${c.changeDueToUSD.change} of that drop.`,
    },
    '0,0': {
      question: (c) => `Did ${c.commodity} change at all?`,
      explanation: () =>
        `No notable movement from either side — USD or market pressures were flat.`,
    },
    '0,1': {
      question: (c) => `What drove the ${c.totalChange.change} change?`,
      explanation: (c) =>
        `Buyers pushed prices up by ${c.changeDueToTrade.change}, even though the US Dollar held steady.`,
    },
    '0,-1': {
      question: (c) => `What drove the ${c.totalChange.change} change?`,
      explanation: (c) =>
        `Sellers drove prices down by ${c.changeDueToTrade.change}, while the US Dollar stayed flat.`,
    },
    '1,0': {
      question: (c) => `What drove the ${c.totalChange.change} change?`,
      explanation: (c) =>
        `A weaker US Dollar lifted prices by ${c.changeDueToUSD.change}, despite little trading movement.`,
    },
    '-1,0': {
      question: (c) => `What drove the ${c.totalChange.change} change?`,
      explanation: (c) =>
        `US Dollar strength shaved off ${c.changeDueToUSD.change}, even though trading was calm.`,
    },
  }

  const usdSign = sign(changeDueToUSD.changeVal)
  const tradeSign = sign(changeDueToTrade.changeVal)
  const key = `${usdSign},${tradeSign}`
  const tpl = TEMPLATES[key] || TEMPLATES['0,0']

  const getQuestionMessage = () => tpl.question(commodityData)
  const getExplanationMessage = () => tpl.explanation(commodityData)

  // widget variant (text + value)
  const getWidgetMessage = () => {
    if (totalChange.changeVal > 0) {
      return {
        text: `Did ${commodityData.commodity} really go up`,
        value: `${totalChange.change} (${totalChange.percentage}%)?`,
      }
    }
    if (totalChange.changeVal < 0) {
      return {
        text: `Did ${commodityData.commodity} really go down`,
        value: `${totalChange.change} (${totalChange.percentage}%)?`,
      }
    }
    return {
      text: `Did ${commodityData.commodity} change at all?`,
      value: '',
    }
  }

  return {
    getQuestionMessage,
    getExplanationMessage,
    getWidgetMessage,
    commodityData,
  }
}

export default useGoldIndexMessages
