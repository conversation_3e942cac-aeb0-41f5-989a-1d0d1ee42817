'use client'

import { useRef } from 'react'
import { useTradingViewWidget } from '../hooks/useTradingViewWidget'
import { TradingViewWidgetProps } from '../types/TradingViewWidget'
import { ErrorDisplay } from './ErrorDisplay'
import { LoadingIndicator } from './LoadingIndicator'

/**
 * TradingView chart widget component
 * This component renders a TradingView chart widget
 */
export default function TradingViewWidget({
  symbol,
  onError,
  theme = 'light',
  autosize = true,
  height = 500,
  width = 800,
  interval = 'D',
  timezone = 'Etc/UTC',
  style = '1',
  locale = 'en',
  toolbar_bg = '#f1f3f6',
  enable_publishing = false,
  withdateranges = true,
  hide_side_toolbar = false,
  allow_symbol_change = true,
  save_image = true,
  show_popup_button = true,
  popup_width = '1000',
  popup_height = '650',
  container_id,
}: TradingViewWidgetProps) {
  const container = useRef<HTMLDivElement>(null)

  // Use the custom hook to handle widget initialization
  const { isLoading, isValidSymbol, containerId } = useTradingViewWidget({
    symbol,
    onError,
    theme,
    autosize,
    height,
    width,
    interval,
    timezone,
    style,
    locale,
    toolbar_bg,
    enable_publishing,
    withdateranges,
    hide_side_toolbar,
    allow_symbol_change,
    save_image,
    show_popup_button,
    popup_width,
    popup_height,
    container_id,
  })

  // Handle invalid symbol
  if (!isValidSymbol) {
    return <ErrorDisplay message={`Invalid TradingView symbol: ${symbol}`} />
  }

  return (
    <div
      id={containerId}
      ref={container}
      style={{
        height: autosize ? '100%' : `${height}px`,
        width: autosize ? '100%' : `${width}px`,
      }}
    >
      {isLoading && <LoadingIndicator />}
    </div>
  )
}
