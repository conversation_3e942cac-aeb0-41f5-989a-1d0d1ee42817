import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import LatestNewsSection from '~/src/components/CommodityContentDetailChart/LatestNewsSection'

const RightContent: FC<{
  classNames: string
  hideLatestNewsHeader?: boolean
}> = ({ classNames, hideLatestNewsHeader }) => {
  return (
    <aside className={classNames}>
      <AdvertisingSlot
        id={'right-rail-1'}
        className={'mx-auto hidden min-h-[250px] w-[300px] desktop:flex'}
      />
      <div className="mt-[15px]">
        <LatestNewsSection hideLatestNewsHeader={hideLatestNewsHeader} />
      </div>
    </aside>
  )
}

export default RightContent
