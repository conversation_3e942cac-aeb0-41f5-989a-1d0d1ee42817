import { List, Root, Viewport } from '@radix-ui/react-navigation-menu'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import AboutItem from './AboutItem/AboutItem'
import BuySellItem from './BuySellItem/BuySellItem'
import ChartsItem from './ChartsItem/ChartsItem'
import JewelerItem from './JewelerItem/JewelerItem'
import MarketsItem from './MarketsItem/MarketsItem'
import MiningItem from './MiningItem/MiningItem'
import { NavLogo } from './NavLogo'
import styles from './NavVer2.module.scss'

import React, { useEffect, useRef, useState } from 'react'
import { IoCloseOutline, IoMenu } from 'react-icons/io5'
import LoginMenuIcon from '~/src/components/Auth/Menu/LoginMenuIcon'

import Link from 'next/link'
import BaseMetalsItem from '~/src/components/NavVer2/BaseMetalsItem/BaseMetalsItem'
import useScreenSize from '~/src/utils/useScreenSize'
import { FavoritesBar } from '../FavoritesBar/FavoritesBar'
import NewsItem from './NewsItem/NewsItem'
import QuotesItem from './QuotesItem/QuotesItem'
import Search from './Search/Search'

/**
 * Navigation component props interface
 *
 * @interface NavProps
 * @property {React.RefObject<HTMLElement>} [headerRef] - Reference to the header element for height calculations
 * @property {(val: boolean) => void} [onMenuMobile] - Callback function triggered when mobile nav state changes
 * @property {(val: boolean) => void} [onShowHide] - Callback function triggered when nav visibility changes
 * @property {boolean} [wide] - Whether to use full width layout (default: false)
 */
interface NavProps {
  headerRef?: any
  onMenuMobile?: (val: boolean) => void
  onShowHide?: (val: boolean) => void
  wide?: boolean
}

/**
 * Main navigation component for Kitco website
 *
 * Provides a responsive navigation bar with:
 * - Desktop horizontal menu with dropdown support
 * - Mobile vertical menu with hamburger toggle
 * - Search functionality
 * - Dynamic show/hide behavior based on scroll
 * - Login menu integration
 * - Favorites bar integration
 *
 * @component
 * @param {NavProps} props - The component props
 * @returns {JSX.Element} The rendered navigation component
 */
const Nav: React.FC<NavProps> = ({
  headerRef,
  onMenuMobile,
  onShowHide,
  wide = false,
}: NavProps) => {
  const router = useRouter()

  /** State for controlling mobile navigation visibility */
  const [mobileNavActivate, setMobileNavActivate] = useState(false)

  /** Hook for responsive design breakpoint detection */
  const { isDesktopForNavBar } = useScreenSize()

  /** State for storing dropdown menu positioning offset */
  const [offset, setOffset] = React.useState<any>()

  /** Reference to the navigation list element for positioning calculations */
  const [list, setList] = React.useState<any>()

  /** Current active menu value for Radix navigation menu */
  const [value, setValue] = React.useState<any>()

  /** Tracks last scroll position for scroll direction detection */
  let lastScrollTop = 0

  /** Reference to the mobile menu toggle button */
  const buttonRef = useRef(null)

  /** Reference to track if user is currently scrolling */
  const isScrollRef = useRef(false)

  /** State to track when a navigation item is clicked */
  const [itemClick, setItemClick] = useState(false)

  /** Header height extracted from headerRef for scroll calculations */
  const heightHeader = Number(headerRef?.heightHeader)

  /** Advertisement banner height extracted from headerRef for scroll calculations */
  const heightAds = Number(headerRef?.heightAds)

  /**
   * Effect to handle custom button click events
   * Listens for global 'buttonClicked' events to manage navigation state
   */
  useEffect(() => {
    const handleButtonClick = () => {
      setItemClick(true)
    }
    window.addEventListener('buttonClicked', handleButtonClick)
    return () => {
      window.removeEventListener('buttonClicked', handleButtonClick)
    }
  }, [])

  /**
   * Effect to handle scroll-based navigation visibility
   * Controls when the navigation bar should show/hide based on scroll direction and position
   */
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop

      // Don't hide nav when scrolling up within header/ads area
      if (
        lastScrollTop > scrollTop &&
        lastScrollTop <= heightAds &&
        scrollTop <= heightHeader
      ) {
        isScrollRef.current = false
        lastScrollTop = scrollTop <= 0 ? 0 : scrollTop
        if (typeof onShowHide === 'function') {
          onShowHide(null)
        }
        return
      }

      // Don't hide nav when mobile menu is active or item was clicked
      if (
        (!isDesktopForNavBar &&
          buttonRef.current === document.activeElement &&
          mobileNavActivate) ||
        itemClick
      ) {
        setItemClick(false)
        return
      }

      // Hide nav on scroll down, show on scroll up
      if (scrollTop > lastScrollTop) {
        isScrollRef.current = true
        if (typeof onShowHide === 'function') {
          onShowHide(true)
        }
      } else {
        isScrollRef.current = false
        if (typeof onShowHide === 'function') {
          onShowHide(false)
        }
      }

      lastScrollTop = scrollTop <= 0 ? 0 : scrollTop
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [lastScrollTop, itemClick, mobileNavActivate, heightHeader])

  /**
   * Effect to handle mobile navigation state changes
   * Manages body scroll behavior and triggers parent callbacks
   */
  useEffect(() => {
    if (mobileNavActivate) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }

    // Check if the onMenuMobile function is a function before calling it
    if (typeof onMenuMobile === 'function') {
      onMenuMobile(mobileNavActivate)
    }
  }, [mobileNavActivate])

  /**
   * Handles dropdown menu positioning updates
   * Calculates the horizontal offset for dropdown menus based on trigger position
   *
   * @param {HTMLElement} trigger - The element that triggered the dropdown
   * @param {string} itemValue - The value of the menu item
   * @returns {HTMLElement} The trigger element
   */
  const onNodeUpdate = (trigger, itemValue) => {
    if (isScrollRef.current) {
      setOffset(undefined)
      return
    }
    if (trigger && list && value === itemValue) {
      const listWidth = list?.offsetWidth
      const listCenter = listWidth / 2

      const triggerOffsetRight =
        listWidth - trigger.offsetLeft - trigger.offsetWidth

      setOffset(Math.round(listCenter - triggerOffsetRight))
    } else if (value === '') {
      setOffset(undefined)
    }
    return trigger
  }

  /**
   * Array of menu item titles in display order
   * Used for rendering navigation items with proper labels
   */
  const menuTitle = [
    'BUY/SELL GOLD & SILVER',
    'Precious Metals',
    'Cryptos',
    'Base Metals',
    'Markets',
    'Mining',
    'News',
    'About',
  ]

  /**
   * Array of menu item components corresponding to menuTitle
   * Each component handles its own dropdown content and behavior
   */
  const menuItems = [
    BuySellItem,
    QuotesItem,
    ChartsItem,
    BaseMetalsItem,
    MarketsItem,
    MiningItem,
    NewsItem,
    AboutItem,
  ]

  return (
    <>
      <Root
        id="nav2"
        value={value}
        onValueChange={setValue}
        orientation={!isDesktopForNavBar ? 'vertical' : 'horizontal'}
        className={clsx(
          'relative w-full',
          'px-2',
          mobileNavActivate ? 'h-auto' : 'h-14',
          router.pathname.includes('/news/video')
            ? 'bg-[#0F181D]'
            : 'bg-ktc-black',
        )}
      >
        <div
          className={clsx(
            wide
              ? 'mx-auto h-full max-w-[2360px] px-2'
              : 'mx-auto h-full max-w-[1240px] px-2',
            'relative flex flex-wrap overflow-hidden',
            'desktop:flex-nowrap desktop:justify-between',
          )}
        >
          <NavLogo />
          <div
            className={clsx(
              mobileNavActivate ? 'overflow-x-hidden mb-5 order-last' : '',
              'flex items-start justify-start desktop:items-center desktop:justify-between left-0 mx-auto',
            )}
          >
            <List
              ref={setList}
              className={clsx(
                'left-0 mx-auto w-full',
                'flex-col',
                'gap-2 desktop:order-2 desktop:w-auto desktop:flex-row desktop:gap-0',
                'mb-5 desktop:mb-0',
                mobileNavActivate ? 'flex' : 'hidden desktop:flex',
              )}
            >
              {menuTitle.map((item, index) => {
                const MenuItem = menuItems[index]
                return (
                  <MenuItem
                    key={index}
                    value={item}
                    onNodeUpdate={onNodeUpdate}
                  />
                )
              })}

              <div className="block md:hidden border-t border-gray-100/50 pt-4 mt-3">
                <div className="text-white pb-3">
                  <Link
                    href="https://forum.kitco.com/categories?utm_source=kitco_website&utm_medium=navbar&utm_campaign=forum_navigation"
                    target="_blank"
                    className="whitespace-nowrap text-sm leading-5 text-white hover:underline font-bold"
                  >
                    {' '}
                    Kitco Forum{' '}
                    <span className="inline-block rounded-md bg-ktc-blue ml-2 px-1 py-0.5 text-xs font-medium text-white hover:bg-kitco-black">
                      NEW
                    </span>
                  </Link>
                </div>
                <JewelerItem
                  onNodeUpdate={onNodeUpdate}
                  key="jeweler-08"
                  value="Jeweler Resources"
                />
              </div>
            </List>
          </div>
          <List
            className={clsx(
              'h-14 max-h-full w-full',
              'z-[2147483647] flex justify-end',
              'order-2 self-start',
              'desktop:order-last',
            )}
          >
            <Search
              mobileNavActivate={mobileNavActivate}
              toggleMobileNav={() => setMobileNavActivate(!mobileNavActivate)}
            />
            <li className={clsx(styles.hamburgerContainer)}>
              <button
                type="button"
                ref={buttonRef}
                onClick={() => setMobileNavActivate(!mobileNavActivate)}
              >
                {!mobileNavActivate ? (
                  <IoMenu size="28px" color="white" />
                ) : (
                  <IoCloseOutline size="28px" color="white" />
                )}
              </button>
            </li>
            {process.env.NEXT_PUBLIC_LOGIN_MENU === 'true' && (
              <li className="flex items-center bg-transparent px-4">
                <LoginMenuIcon />
              </li>
            )}
          </List>
        </div>
        {isDesktopForNavBar && (
          <div
            className={clsx(
              'perspective-2000 absolute left-0 z-[20000000] flex w-full justify-center',
              'mx-auto',
            )}
          >
            <Viewport
              style={{
                paddingTop: '4px',
                // Avoid transitioning from initial position when first opening
                display: !offset ? 'none' : undefined,
                transform: `translateX(${offset}px)`,
                top: '100%',
                transition: 'all 0.5s ease',
              }}
            />
          </div>
        )}
      </Root>
      {/* don't show the favorites bar on video news paths */}
      {!router.pathname.includes('/video') ? (
        <div id="nav2-favorites" className="hidden md:block">
          <FavoritesBar />{' '}
        </div>
      ) : null}
    </>
  )
}

export default Nav
