import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const markets: SectionItems[] = [
  {
    name: 'DOW',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$DOWI',
  },
  {
    name: 'NASDAQ',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$NASX',
  },
  {
    name: 'S&P 500',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$SPX',
  },
  {
    name: 'NYSE',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$NYA',
  },
  { name: 'Forex Major Rates', href: '/price/forex' },
  { name: 'Kitco Global Index', href: '/markets/kitco-gold-index' },
  { name: 'Most Active Stocks', href: '/markets/stocks/mostactive' },
  {
    name: 'Stocks Gainers and Losers',
    href: '/markets/stocks',
  },
]

export const mining: SectionItems[] = [
  { name: 'Gold', href: '/markets/mining/-MIGL' },
  { name: 'Silver', href: '/markets/mining/-MISI' },
  { name: 'Iron', href: '/markets/mining/-MIIR' },
  { name: 'Base Metals', href: '/markets/mining/-MEPF' },
  { name: 'Non-Ferous', href: '/markets/mining/-MINF' },
]

export const cryptos: SectionItems[] = [
  {
    name: 'Bitcoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/bitcoin',
  },
  {
    name: 'Ethereum',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ethereum',
  },
  {
    name: 'Litecoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/litecoin',
  },
  {
    name: 'Monero',
    href: '/price/crypto/[name]',
    as: '/price/crypto/monero',
  },
  {
    name: 'Ripple',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ripple',
  },
  {
    name: 'Dash',
    href: '/price/crypto/[name]',
    as: '/price/crypto/dash',
  },
]

export const futures: SectionItems[] = [
  { name: 'Metals', href: '/markets/futures/metals' },
  { name: 'Energies', href: '/markets/futures/energies' },
  { name: 'Grains', href: '/markets/futures/grains' },
  { name: 'Indices', href: '/markets/futures/indices' },
  // { name: "Softs", href: "/markets/futures/softs" },
  { name: 'Meats', href: '/markets/futures/meats' },
  { name: 'Currencies', href: '/markets/futures/currencies' },
]

export const more: SectionItems[] = [
  { name: 'Metals', href: '/markets/metals' },
  { name: 'Stocks Gainers and Losers', href: '/markets/stocks' },
]

function MarketsMenu() {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList
          title="Markets Overview"
          titleUrl="/markets"
          items={markets}
        />
        <SectionList title="Mining" titleUrl="/markets/mining" items={mining} />
      </Navigation.SubMenuColumn>
      <Navigation.SubMenuColumn>
        <SectionList
          title="Cryptocurrencies"
          titleUrl="/price/crypto"
          items={cryptos}
        />
      </Navigation.SubMenuColumn>
      <Navigation.SubMenuColumn>
        <SectionList
          title="Futures"
          titleUrl="/markets/futures"
          items={futures}
        />
        <SectionList title="More" items={more} />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default MarketsMenu
