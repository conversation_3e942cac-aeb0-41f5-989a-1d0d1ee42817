import dynamic from 'next/dynamic'
import type { FC } from 'react'

// Import TradingView widget dynamically for client-side rendering only
const TradingViewWidget = dynamic(
  () => import('~/src/services/tradingview/components/TradingViewWidget'),
  { ssr: false },
)

export type LeaderTypes = 'active' | 'gainers' | 'losers'

interface TradingViewStockLeadersProps {
  leaderType?: LeaderTypes
  showMore?: boolean
  className?: string
}

/**
 * TradingView-based stock leaders component that replaces BarchartsLeadersCell
 * Uses TradingView's built-in market screener functionality
 */
const TradingViewStockLeaders: FC<TradingViewStockLeadersProps> = ({
  leaderType = 'active',
  showMore = false,
  className,
}) => {
  const getTitle = (): string => {
    switch (leaderType) {
      case 'active':
        return 'Most Active Stocks'
      case 'gainers':
        return 'Top Gainers'
      case 'losers':
        return 'Top Losers'
      default:
        return 'Stock Leaders'
    }
  }

  const handleError = (error: string) => {
    console.error(`TradingView stock leaders error for ${leaderType}:`, error)
  }

  return (
    <div className={className}>
      <div className="mb-4">
        <h2 className="text-xl font-semibold">{getTitle()}</h2>
      </div>
      <div className="bg-white border border-gray-200 rounded-md overflow-hidden">
        <TradingViewWidget
          symbol="NASDAQ:AAPL" // Default symbol, screener will override
          onError={handleError}
          theme="light"
          autosize={false}
          height={550}
          interval="D"
          timezone="Etc/UTC"
          style="1"
          locale="en"
          toolbar_bg="#f1f3f6"
          enable_publishing={false}
          withdateranges={false}
          hide_side_toolbar={true}
          allow_symbol_change={false}
          save_image={false}
          show_popup_button={false}
        />
      </div>
      {showMore && (
        <div className="mt-4 text-center">
          <a
            href="/markets/stocks"
            className="text-blue-600 hover:underline font-medium"
          >
            More stocks +
          </a>
        </div>
      )}
    </div>
  )
}

export default TradingViewStockLeaders
