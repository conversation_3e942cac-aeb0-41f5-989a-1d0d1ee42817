import fs from 'fs'
import type { NextApiRequest, NextApiResponse } from 'next'
import path from 'path'

/**
 * Internal API endpoint to serve CSV URL mapping data
 * This prevents exposing the CSV file publicly
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Read the CSV file from the data directory
    // process.cwd() returns the project root, so we need to go to next/data/
    const csvPath = path.join(
      process.cwd(),
      'next',
      'data',
      'bc_to_tv_urls.csv',
    )

    if (!fs.existsSync(csvPath)) {
      // Try alternative path - running from next/ directory
      const altPath = path.join(process.cwd(), 'data', 'bc_to_tv_urls.csv')

      if (fs.existsSync(altPath)) {
        const csvContent = fs.readFileSync(altPath, 'utf-8')
        res.setHeader('Content-Type', 'text/csv')
        res.setHeader('Cache-Control', 'max-age=3600')
        return res.status(200).send(csvContent)
      }

      return res.status(404).json({ error: 'CSV file not found' })
    }

    const csvContent = fs.readFileSync(csvPath, 'utf-8')

    // Set appropriate headers
    res.setHeader('Content-Type', 'text/csv')
    res.setHeader('Cache-Control', 'max-age=3600') // Cache for 1 hour

    // Return the CSV content
    res.status(200).send(csvContent)
  } catch (error) {
    console.error('Error reading CSV file:', error)
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
