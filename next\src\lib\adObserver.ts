declare global {
  interface Window {
    googletag: any
  }
}
// TODO: Allow ability to block line item types (so we dont refresh directs) / line item exclusions - Can detect backfill (AdX) only currently, so we wont refresh directs, but this would also apply to header bidding and amazon I believe.
// TODO: ADD BLUR AND FOCUS EVENT LISTENER TO HANDLE BACKGROUND TAB EDGE CASE
// TODO: Check if user is active (has moved mouse/scrolled/hit key in last X time period) - Is this necessary? We have a max cap. Possible, but introduces complexity and may require central observer.

export class AdObserver {
  inViewValue: number
  slotID: string
  totalRefreshes: number
  maxRefreshes: number
  viewDurationValue: number
  interval: number | null
  slot: {}
  refreshTime: number
  inViewPercent: number
  debug: boolean
  constructor(slotID, slot, time, debug) {
    this.slotID = slotID
    this.refreshTime = time
    this.slot = slot
    this.inViewPercent = 70
    this.totalRefreshes = 0
    this.maxRefreshes = 7
    this.viewDurationValue = 0
    this.interval = null
    this.inViewValue = 0
    this.debug = debug
  }

  reset() {
    if (this.interval) {
      clearInterval(this.interval)
      this.interval = null
    }
  }
  set inView(val: number) {
    this.inViewValue = val
    this.viewListener()
  }
  get inView() {
    return this.inViewValue
  }
  set viewDuration(val: number) {
    if (this.debug) {
      console.log('Setting view duration ' + val + ' for unit ' + this.slotID)
    }
    this.viewDurationValue = val
    this.viewDurationListener(val)
  }
  get viewDuration() {
    return this.viewDurationValue
  }
  private viewListener() {
    if (this.inView >= this.inViewPercent) {
      this.intervalStart()
    } else {
      if (this.interval) {
        this.reset()
      }
    }
  }
  private intervalStart() {
    if (!this.interval && this.inView >= this.inViewPercent) {
      this.interval = window.setInterval(
        this.increaseViewDuration.bind(this),
        1000,
      )
    }
  }
  increaseViewDuration() {
    this.viewDuration = this.viewDuration + 1
  }
  refresh(slot) {
    if (this.totalRefreshes >= this.maxRefreshes) {
      // @ts-ignore
      clearInterval(this.interval)
      return
    }

    this.viewDuration = 0
    this.totalRefreshes = this.totalRefreshes + 1
    var that = this
    window.googletag = window.googletag || { cmd: [] }

    const refreshCount = this.totalRefreshes.toString()

    window.googletag.cmd.push(function () {
      if (that.slot) {
        slot.setTargeting('refresh_count', refreshCount)
        window.googletag.pubads().refresh([slot])
      }
    })
  }

  private viewDurationListener(val: number) {
    if (this.viewDurationValue >= this.refreshTime) {
      if (this.debug) {
        console.log(
          'This ad should refresh as its been in view for at least ' +
            val +
            ' seconds',
        )
      }
      this.refresh(this.slot)
    }
  }
}
