import type { GetServerSideProps } from 'next'
import { useRouter } from 'next/router'
import type { FC } from 'react'
import { useEffect } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { AttributionSection } from '~/src/components/AttributionSection/AttributionSection'
import Layout from '~/src/components/Layout/Layout'
import { StocksPageTitle } from '~/src/components/StocksPageTitle/StocksPageTitle'

import AdvancedChartWidget from '~/src/services/tradingview/components/AdvancedChartWidget'
import { CompanyProfileWidget } from '~/src/services/tradingview/components/CompanyProfileWidget'
import { FundamentalDataWidget } from '~/src/services/tradingview/components/FundamentalDataWidget'
import { NYTimeDisplay } from '~/src/services/tradingview/components/NYTimeDisplay'
import { SymbolInfoWidget } from '~/src/services/tradingview/components/SymbolInfoWidget'
import { TechnicalAnalysisWidget } from '~/src/services/tradingview/components/TechnicalAnalysisWidget'
import { getSymbolFromElastic } from '~/src/services/tradingview/utils/getSymbolFromElastic'
import { symbolExistsInElastic } from '~/src/services/tradingview/utils/symbolExistsInElastic'

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const urlSymbol = ctx.query.symbol as string

  // Convert URL-friendly symbol back to original format
  const symbol = urlSymbol.replace(/-/g, ':')

  console.log(
    `[DEBUG] Processing symbol: ${symbol} (original URL: ${urlSymbol})`,
  )

  // Basic validation - allow alphanumeric chars, dots, underscores, dashes, colons, and slashes
  const isValidFormat = /^[A-Za-z0-9\._\-:\/]+$/.test(symbol)
  if (!isValidFormat) {
    console.log(`[DEBUG] Invalid format for symbol: ${symbol}`)
    return { redirect: { destination: '/404', permanent: false } }
  }

  // Check if symbol exists in Elasticsearch
  console.log(`[DEBUG] Checking if symbol exists in Elasticsearch: ${symbol}`)
  const existsInElastic = await symbolExistsInElastic(symbol)
  console.log(`[DEBUG] Elasticsearch result: ${existsInElastic}`)

  if (existsInElastic) {
    console.log(`[DEBUG] Symbol found in Elasticsearch, fetching data...`)
    const symbolData = await getSymbolFromElastic(symbol)
    console.log(
      `[DEBUG] Symbol data from Elasticsearch:`,
      symbolData ? 'Found' : 'Not found',
    )
    return {
      props: {
        symbol: urlSymbol,
        symbolData: symbolData || null,
      },
    }
  }

  // If symbol not found in Elasticsearch, redirect to 404
  console.log(`[DEBUG] Symbol not found in Elasticsearch, redirecting to 404`)
  return { redirect: { destination: '/404', permanent: false } }
}

const StockSymbol: FC<{ symbol: string; symbolData: any }> = ({
  symbol,
  symbolData,
}) => {
  const router = useRouter()

  // Convert URL-friendly symbol back to original format for TradingView
  const tradingViewSymbol = symbol.replace(/-/g, ':')

  // Client-side validation
  useEffect(() => {
    const originalSymbol = symbol.replace(/-/g, ':')
    const isValidFormat = /^[A-Za-z0-9\._\-:\/]+$/.test(originalSymbol)

    if (!isValidFormat) {
      router.push('/404')
    }
  }, [symbol, router])

  const symbolTitle = symbolData?.['symbol'] || tradingViewSymbol

  return (
    <Layout title={symbolTitle} wide={true}>
      <div className="px-4 sm:px-8">
        {/* Header Section */}
        <div className="w-full mb-6">
          <StocksPageTitle symbol={tradingViewSymbol} symbolData={symbolData} />

          {/* Date/Time and Attribution Row */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-6 space-y-3 md:space-y-0">
            <NYTimeDisplay />
            <div className="text-left md:text-right">
              <p className="text-xs md:text-sm text-ktc-date-gray">
                Market Data and Widgets Technology provided by{' '}
                <a
                  href="https://www.tradingview.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  TradingView
                </a>
              </p>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 mb-8">
          {/* Left Column - Chart and Symbol Info */}
          <div className="xl:col-span-3">
            <div className="mb-6">
              <SymbolInfoWidget symbol={tradingViewSymbol} />
            </div>

            <div className="mb-6">
              <AdvancedChartWidget
                symbol={tradingViewSymbol}
                height={800}
                mobileHeight={400}
              />
            </div>
          </div>

          {/* Right Column - Large Ad Banner */}
          <div className="xl:col-span-1">
            <div className="sticky top-20">
              <AdvertisingSlot
                id={'right-rail-large'}
                className="mx-auto h-[600px] max-w-[336px] flex items-center justify-center no-print bg-gray-300"
              />
            </div>
          </div>
        </div>

        {/* Full Width Ad Banner */}
        <div className="w-full mb-8 hidden lg:block">
          <AdvertisingSlot
            id={'stocks-banner-970x250'}
            className="mx-auto h-[250px] w-[300px] lg:w-[970px] flex items-center justify-center no-print bg-gray-300"
          />
        </div>

        {/* Bottom Content Grid - 3 Columns */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-8">
          {/* Left Column - Company Profile */}
          <div className="lg:col-span-1">
            <div className="w-full h-[300px] lg:h-[770px] mb-6">
              <CompanyProfileWidget
                symbol={symbolData?.['symbol-fullname'] || tradingViewSymbol}
                colorTheme="light"
                isTransparent={false}
                locale="en"
              />
            </div>
          </div>

          {/* Middle Column - Fundamental Data */}
          <div className="lg:col-span-1">
            <div className="mb-6 h-[770px]">
              <FundamentalDataWidget symbol={tradingViewSymbol} />
            </div>
          </div>

          {/* Right Column - Technical Analysis */}
          <div className="lg:col-span-1">
            <div className="w-full h-[300px] lg:h-[770px]">
              <TechnicalAnalysisWidget
                symbol={tradingViewSymbol}
                interval="1D"
                colorTheme="light"
                displayMode="single"
                isTransparent={false}
                locale="en"
                showIntervalTabs={true}
              />
            </div>
          </div>
        </div>

        {/* Bottom Full Width Ad Banner */}
        <div className="w-full mb-8">
          <AdvertisingSlot
            id={'stocks-bottom-banner-970x250'}
            className="mx-auto h-[250px] w-[350px] lg:w-[970px] flex items-center justify-center no-print bg-gray-300"
          />
        </div>
      </div>

      {/* Attribution Section - Full Width */}
      <div className="w-full">
        <AttributionSection />
      </div>
    </Layout>
  )
}

export default StockSymbol
