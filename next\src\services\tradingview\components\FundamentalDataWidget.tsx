import type { FC } from 'react'
import { useEffect, useRef } from 'react'

interface FundamentalDataWidgetProps {
  symbol: string
}

export const FundamentalDataWidget: FC<FundamentalDataWidgetProps> = ({
  symbol,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    // Clear any existing content
    containerRef.current.innerHTML = ''

    // Use 100% height
    const height = '100%'

    // Create the widget container
    const widgetContainer = document.createElement('div')
    widgetContainer.className = 'tradingview-widget-container'
    widgetContainer.style.height = height
    widgetContainer.style.width = '100%'
    widgetContainer.style.maxWidth = '800px'

    // Create the widget content div
    const widgetContent = document.createElement('div')
    widgetContent.className = 'tradingview-widget-container__widget'

    // Create the copyright div
    const copyrightDiv = document.createElement('div')
    copyrightDiv.className = 'tradingview-widget-copyright'
    copyrightDiv.innerHTML = `
      <a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank">
        <span class="blue-text">Track all markets on TradingView</span>
      </a>
    `

    // Create and configure the script
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-financials.js'
    script.async = true
    script.innerHTML = JSON.stringify({
      isTransparent: false,
      largeChartUrl: `${process.env.NEXT_PUBLIC_URL}/markets/stocks/${symbol}`,
      displayMode: 'regular',
      width: '100%',
      height: '100%',
      colorTheme: 'light',
      symbol: symbol,
      locale: 'en',
    })

    // Assemble the widget
    widgetContainer.appendChild(widgetContent)
    widgetContainer.appendChild(copyrightDiv)
    widgetContainer.appendChild(script)

    // Add to container
    containerRef.current.appendChild(widgetContainer)

    // Cleanup function
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
    }
  }, [symbol])

  return (
    <div
      ref={containerRef}
      className="tradingview-fundamental-data-widget"
      style={{ height: '100%', maxWidth: '800px', width: '100%' }}
    />
  )
}
