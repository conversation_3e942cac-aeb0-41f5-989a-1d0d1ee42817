import clsx from 'clsx'
import dayjs from 'dayjs'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import { AdvertisingSlot } from 'react-advertising'
import OtherIndicesCell from '~/src/components-markets/OtherIndicesCell/OtherIndicesCell'
import BaseMetalsQuotesCell from '~/src/components-metals/BaseMetalsQuotesCell/BaseMetalsQuotesCell'
import Divider from '~/src/components/Divider/Divider'
import Layout from '~/src/components/Layout/Layout'
import { PressReleaseSidebar } from '~/src/components/PressReleases/PressReleaseSidebar'
import { InvestmentTrends } from '~/src/components/investment-trends/investment-trends.component'
import { NewsMiningTrendsCell } from '~/src/components/news-mining-trends/news-mining-trends.component'
import { pressReleases } from '~/src/lib/PressReleases/Queries'
import { markets } from '~/src/lib/markets-factory.lib'
import { metals } from '~/src/lib/metals-factory.lib'
import { news } from '~/src/lib/news-factory.lib'
import gridAreas from '~/src/styles/gridAreas.module.scss'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import * as timestamps from '~/src/utils/timestamps'
import styles from './base-metals.module.scss'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      metals.allMetalsQuote({
        variables: {
          timestamp: timestamps.current(),
          currency: 'USD',
        },
      }),
      markets.regionIndices({
        variables: {
          timestamp: timestamps.current(),
        },
      }),
      metals.londonFix({
        variables: {
          year: dayjs().year().toString(),
        },
      }),
      news.nodeListQueue({
        variables: {
          limit: 10,
          offset: 0,
          queueId: 'latest_news',
        },
      }),
      metals.shanghaiFix({
        variables: {
          timestamp: timestamps.current(),
          symbol: 'SHAU',
          currency: 'CNY',
        },
      }),
      pressReleases.nodeListPressReleasesQueue({
        variables: {
          limit: 6,
          offset: 0,
          queueId: 'press_releases',
        },
        options: {
          enabled: true,
        },
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}

const AllMetalQuotes: NextPage = () => {
  return (
    <Layout title="Base & Industrial Metals | Base Metal Prices, Charts and News | KITCO">
      <Head>
        <meta
          name="description"
          content="The world’s largest base metals resources - Copper, Aluminum, Zinc, Lead and Nickel - Live prices, historical charts, news and expert opinions."
        />
      </Head>
      <h1
        className="pl-2 text-[32px] uppercase text-kitco-black md:text-[48px]"
        style={{ fontFamily: 'Bebas Neue' }}
      >
        BASE METALS
      </h1>
      <div className={clsx('px-2', styles.tabletGridOrder)}>
        <div
          className={clsx(
            'hidden desktop:flex desktop:w-[200px] desktop:flex-col desktop:gap-5',
          )}
        >
          <OtherIndicesCell />
          <AdvertisingSlot
            id={'left-rail-1'}
            className="mx-auto h-[600px] w-[160px] no-print"
          />
        </div>
        <div
          className={clsx(
            'contents desktop:flex desktop:flex-col desktop:gap-5',
          )}
        >
          <BaseMetalsQuotesCell title="Base Metals Prices Today" />
          <AdvertisingSlot
            id="mining-content-billboard"
            className="mx-auto my-[20px] flex min-h-[600px] w-[100%] max-w-[300px] items-center justify-center border-[1px] border-[#ccc] native-sm:h-[252px] native-sm:min-h-[200px] native-sm:max-w-[970px] no-print"
          />
          {/* {(isMobile || isTablet) && (
            <AdvertisingSlot
              id={"banner-2"}
              className="h-[250px] w-[300px] mx-auto mb-8 tablet:h-[90px] tablet:w-[728px] no-print"
            />
          )} */}
          {/* <AdvertisingSlot
            id={"banner-3"}
            className="h-[250px] w-[300px] mx-auto mb-8 tablet:h-[90px] tablet:w-[728px] no-print"
          /> */}
          <Divider />
          <div
            className={clsx(
              'flex grid-cols-7 flex-col gap-10 pb-5 desktop:grid',
              gridAreas.ll,
            )}
          >
            <div className="col-span-4">
              <NewsMiningTrendsCell />
            </div>
            <div className="col-span-3">
              <InvestmentTrends />
            </div>
          </div>
        </div>
        <div
          className={clsx('hidden desktop:flex desktop:flex-col desktop:gap-5')}
        >
          <PressReleaseSidebar width="300px" />
          <AdvertisingSlot
            id={'right-rail-2'}
            className="sticky top-4 mx-auto h-[600px] w-[300px] no-print"
          />
        </div>
      </div>
      <AdvertisingSlot
        id={'footer'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
      />
    </Layout>
  )
}

export default AllMetalQuotes
