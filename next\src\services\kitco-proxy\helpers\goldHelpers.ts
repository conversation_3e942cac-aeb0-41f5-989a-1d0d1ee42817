import {
  fetchMultipleSymbolsData,
  fetchSymbolData,
} from '../api/fetchKitcoData'
import type { KitcoValue } from '../types/KitcoValue'

/**
 * Common gold-related symbols
 */
export const GOLD_SYMBOLS = {
  XAU: 'XAU', // Gold price
  HUI: 'HUI', // Gold bugs index
} as const

/**
 * Fetches XAU (Gold) data specifically
 *
 * @param version - API version (optional)
 * @returns Promise resolving to XAU data or null if not found
 */
export async function fetchXAUData(
  version?: string,
): Promise<KitcoValue | null> {
  return fetchSymbolData(GOLD_SYMBOLS.XAU, version)
}

/**
 * Fetches HUI (Gold Bugs Index) data specifically
 *
 * @param version - API version (optional)
 * @returns Promise resolving to HUI data or null if not found
 */
export async function fetchHUIData(
  version?: string,
): Promise<KitcoValue | null> {
  return fetchSymbolData(GOLD_SYMBOLS.HUI, version)
}

/**
 * Fetches both XAU and HUI data in a single request
 *
 * @param version - API version (optional)
 * @returns Promise resolving to an object with XAU and HUI data
 */
export async function fetchGoldRatiosData(version?: string): Promise<{
  xau: KitcoValue | null
  hui: KitcoValue | null
}> {
  const symbolsData = await fetchMultipleSymbolsData(
    [GOLD_SYMBOLS.XAU, GOLD_SYMBOLS.HUI],
    version,
  )

  return {
    xau: symbolsData.find((d) => d.symbol === GOLD_SYMBOLS.XAU) || null,
    hui: symbolsData.find((d) => d.symbol === GOLD_SYMBOLS.HUI) || null,
  }
}

/**
 * Calculates the XAU/HUI ratio
 *
 * @param xauData - XAU price data
 * @param huiData - HUI index data
 * @returns Ratio value or null if calculation cannot be performed
 */
export function calculateXAUHUIRatio(
  xauData: KitcoValue,
  huiData: KitcoValue,
): number | null {
  if (!xauData || !huiData || huiData.price === 0) {
    return null
  }

  return xauData.price / huiData.price
}
