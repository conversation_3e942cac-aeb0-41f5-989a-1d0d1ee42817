import { type QueryClient, useQuery } from '@tanstack/react-query'
import objectHash from 'object-hash'
import { useEffect, useMemo, useState } from 'react'
import type { MiningEquitiesTableQuery } from '~/src/generated'
import { MiningEquitiesQueries } from '~/src/lib/MiningEquities/Queries'
import type MiningEquity from '~/src/types/DataTable/MiningEquity'
import { formatPercentage } from '~/src/utils/Prices/formatPercentage'
import { formatPrice } from '~/src/utils/Prices/formatPrice'
import {
  preloadUrlMapping,
  replaceUrlFromMapping,
  replaceUrlFromMappingSync,
} from '~/src/utils/csvUrlMapping'

/**
 * Get the Mining Equities data
 *
 * @returns {MiningEquity[]}
 */
const useMiningEquitiesData = (): MiningEquity[] => {
  // Get the data from the DB
  const { data } = useQuery(MiningEquitiesQueries.miningEquitiesTable())

  // Store the cached data to prevent re-rendering
  const [cachedData, setCachedData] = useState<MiningEquitiesTableQuery>(
    data ?? {},
  )

  // Track URL mapping loading state
  const [isMappingLoaded, setIsMappingLoaded] = useState(false)

  // Only update the cached data if the data has changed
  useEffect(() => {
    if (!data) return

    if (objectHash(data) !== objectHash(cachedData)) {
      setCachedData(data)
    }
  }, [data])

  // Pre-load URL mapping on client-side
  useEffect(() => {
    preloadUrlMapping()
      .then(() => {
        console.log('✅ URL mapping loaded, updating component state')
        setIsMappingLoaded(true)
      })
      .catch((error) => {
        console.error('❌ Failed to load URL mapping:', error)
        // Still set to true so component doesn't hang
        setIsMappingLoaded(true)
      })
  }, [])

  // Memoize the formatted data - wait for mapping to load
  return useMemo(() => {
    // If mapping isn't loaded yet, return empty array to prevent errors
    if (!isMappingLoaded) {
      console.log('⏳ Waiting for URL mapping to load...')
      return []
    }

    console.log('🔄 Creating table data with cached data:', {
      hasData: !!cachedData,
      hasEquities: cachedData?.GetEquities?.length > 0,
      equitiesCount: cachedData?.GetEquities?.length || 0,
    })

    const result = createTableDataSync(cachedData)
    console.log('✅ Created table data:', {
      resultLength: result.length,
      firstItem: result[0],
      lastItem: result[result.length - 1],
    })

    return result
  }, [cachedData, isMappingLoaded])
}

/**
 * Get the Mining Equities data for SSR
 *
 * @returns {MiningEquity[]}
 */
const useMiningEquitiesDataSSR = async (
  queryClient: QueryClient,
): Promise<MiningEquity[]> => {
  const data = await queryClient.fetchQuery(
    MiningEquitiesQueries.miningEquitiesTable(),
  )

  return await createTableData(data)
}

/**
 * Create the table data (async version for SSR)
 *
 * @param {MiningEquitiesTableQuery} data
 * @returns {Promise<MiningEquity[]>}
 */
async function createTableData(
  data: MiningEquitiesTableQuery,
): Promise<MiningEquity[]> {
  return await formatData(data)
}

/**
 * Create the table data (sync version for client-side)
 *
 * @param {MiningEquitiesTableQuery} data
 * @returns {MiningEquity[]}
 */
function createTableDataSync(data: MiningEquitiesTableQuery): MiningEquity[] {
  return formatDataSync(data)
}

/**
 * Get the top mining equities
 *
 * @param {MiningEquity[]} data
 * @param {string} category
 * @param {number} limit
 * @returns {MiningEquity[]}
 */
export function getTopMiningEquities(
  data: MiningEquity[],
  category = 'ALL',
  limit = 5,
): MiningEquity[] {
  return data
    .filter((item) => {
      const matchesCategory =
        category === 'ALL' ||
        (Array.isArray(item.Category)
          ? item.Category.includes(category)
          : item.Category === category)
      return (
        matchesCategory &&
        item.ChangePercentageVal !== undefined &&
        item.ChangePercentageVal !== null
      )
    })
    .sort((a, b) => {
      // Handle null/undefined values
      if (a.ChangePercentageVal == null) return 1
      if (b.ChangePercentageVal == null) return -1
      return b.ChangePercentageVal - a.ChangePercentageVal
    })
    .slice(0, limit)
}

/**
 * Formats an array of MiningEquitiesTableQuery objects by categorizing and
 * formatting the values.
 *
 * @param {MiningEquitiesTableQuery} data - The array of Mining Data objects to format.
 * @returns {Promise<MiningEquity[]>} - A new array of CommodityData objects with formatted values.
 */
async function formatData(
  data: MiningEquitiesTableQuery,
): Promise<MiningEquity[]> {
  // Return an empty array if there is no data
  if (!data || !data.GetEquities || data?.GetEquities.length <= 0) return []

  const formattedEquities = await Promise.all(
    data?.GetEquities.map(async (item) => {
      return {
        Category: item?.Category ?? '',
        Change: formatPrice({ value: item?.Change }) ?? '0.00',
        ChangePercentage:
          formatPercentage({ value: item?.ChangePercentage }) ?? '0.00',
        ChangePercentageVal: item?.ChangePercentage ?? 0,
        ChangeVal: item?.Change ?? 0,
        Currency: item?.Currency ?? '',
        Exchange: item?.Exchange ?? '',
        High: formatPrice({ value: item?.High }) ?? '0.00',
        HighVal: item?.High ?? 0,
        ID: item?.ID,
        Low: formatPrice({ value: item?.Low }) ?? '0.00',
        LowVal: item?.Low ?? 0,
        Name: item?.Name ?? '',
        Price: formatPrice({ value: item?.Price, min: 2 }) ?? '0.00',
        PriceVal: item?.Price ?? 0,
        Symbol: item?.Symbol,
        SymbolWithPrefix:
          item?.Exchange && item?.Symbol
            ? `${item?.Exchange}:${item?.Symbol}`
            : '',
        SymbolURL: await replaceUrlFromMapping(item?.SymbolURL || ''),
        TVExchange: item?.TVExchange ?? '',
        TVSymbol: item?.TVSymbol ?? '',
        Timestamp: item?.Timestamp,
        Volume: formatPrice({ value: item?.Volume, min: 0 }) ?? '0.00',
        VolumeVal: item?.Volume ?? 0,
      }
    }),
  )

  return formattedEquities
}

/**
 * Formats an array of MiningEquitiesTableQuery objects by categorizing and
 * formatting the values (synchronous version for client-side).
 *
 * @param {MiningEquitiesTableQuery} data - The array of Mining Data objects to format.
 * @returns {MiningEquity[]} - A new array of CommodityData objects with formatted values.
 */
function formatDataSync(data: MiningEquitiesTableQuery): MiningEquity[] {
  // Return an empty array if there is no data
  if (!data || !data.GetEquities || data?.GetEquities.length <= 0) return []

  return data?.GetEquities.map((item) => {
    return {
      Category: item?.Category ?? '',
      Change: formatPrice({ value: item?.Change }) ?? '0.00',
      ChangePercentage:
        formatPercentage({ value: item?.ChangePercentage }) ?? '0.00',
      ChangePercentageVal: item?.ChangePercentage ?? 0,
      ChangeVal: item?.Change ?? 0,
      Currency: item?.Currency ?? '',
      Exchange: item?.Exchange ?? '',
      High: formatPrice({ value: item?.High }) ?? '0.00',
      HighVal: item?.High ?? 0,
      ID: item?.ID,
      Low: formatPrice({ value: item?.Low }) ?? '0.00',
      LowVal: item?.Low ?? 0,
      Name: item?.Name ?? '',
      Price: formatPrice({ value: item?.Price, min: 2 }) ?? '0.00',
      PriceVal: item?.Price ?? 0,
      Symbol: item?.Symbol,
      SymbolWithPrefix:
        item?.Exchange && item?.Symbol
          ? `${item?.Exchange}:${item?.Symbol}`
          : '',
      SymbolURL: replaceUrlFromMappingSync(item?.SymbolURL || ''),
      TVExchange: item?.TVExchange ?? '',
      TVSymbol: item?.TVSymbol ?? '',
      Timestamp: item?.Timestamp,
      Volume: formatPrice({ value: item?.Volume, min: 0 }) ?? '0.00',
      VolumeVal: item?.Volume ?? 0,
    }
  })
}

export { createTableData, useMiningEquitiesData, useMiningEquitiesDataSSR }
