import type MiningEquity from '~/src/types/DataTable/MiningEquity'

/**
 * Filter the data by category
 *
 * @param data
 * @param category
 */
export const filterDataByCategory = (
  data: MiningEquity[],
  category: string,
): MiningEquity[] => {
  if (category === 'ALL') {
    return data
  }

  const filtered = data.filter((item) => {
    const categories = Array.isArray(item.Category)
      ? item.Category
      : [item.Category]

    return categories.some(
      (cat) => cat.toLowerCase() === category.toLowerCase(),
    )
  })

  console.log(`Filtered by category "${category}":`, filtered.length, 'items')
  return filtered
}

/**
 * Filter the data by sub category (Disabled for now)
 *
 * @param data
 * @param subCategory
 */
/*export const filterDataBySubCategory = (
  data: MiningEquity[],
  subCategory: string,
): MiningEquity[] => {
  return subCategory === 'ALL'
    ? data
    : data.filter((item) =>
        Array.isArray(item.SubCategory)
          ? item.SubCategory.includes(subCategory)
          : item.SubCategory === subCategory,
      )
}*/

/**
 * Filter the data by search
 *
 * @param data
 * @param searchValue
 */
export const filterDataBySearch = (
  data: MiningEquity[],
  searchValue: string,
): MiningEquity[] => {
  return searchValue
    ? data.filter((item: MiningEquity) => {
        const { Name, TVSymbol } = item
        return (
          (Name && Name.toLowerCase().includes(searchValue.toLowerCase())) ||
          (TVSymbol &&
            TVSymbol.toLowerCase().includes(searchValue.toLowerCase()))
        )
      })
    : data
}
