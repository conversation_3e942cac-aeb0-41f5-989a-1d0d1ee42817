import { onAuthStateChanged, type User } from 'firebase/auth'
import { type MutableRefObject, useEffect, useRef } from 'react'
import { getStoryID } from '~/src/components/Comment/CommentUtils'
import { useRecaptchaVerification } from '~/src/features/auth/recapcha'
import { getToken } from '~/src/services/coraltalk/Token'
import { auth } from '~/src/services/firebase/config'

type CommentBoxProps = {
  // The ID of the story, used to identify the content in coral talk
  storyID: number
  // The category of the story, used to identify the content in coral talk
  category: string
  // The class name of the Comment Box
  className?: string
  // The ID of the element, used to identify div, must be unique for avoiding conflicts
  elementID?: string
  // Drawer Opened?
  openDrawer?: boolean
}

export type { CommentBoxProps }

/**
 * User Info type
 * This type is used to store the user info and the JWT token
 */
type UserInfo = {
  user: User | null
  jwt_token: string | null
}

/**
 * Create a CommentBox component
 * This component is a Comment Box that contains a Coral Talk comment system
 * It is used to display the comments of a story
 *
 * @param storyID
 * @param category
 * @param className
 * @param elementID
 * @constructor
 */
const CommentBox = ({
  storyID,
  category,
  className,
  elementID,
}: CommentBoxProps) => {
  // The ID of the element, used to identify div, must be unique for avoiding conflicts
  const element_id: string = elementID
    ? `coral_thread_${elementID}`
    : 'coral_thread'

  // The URL of the Coral Talk server
  const URL: string = process.env.NEXT_PUBLIC_CORALTALK_URL

  // Check if the component has mounted for not reloading the page multiple times
  const hasMounted: MutableRefObject<boolean> = useRef(false)

  // The reference of the div element
  const ref = useRef(null)

  // Custom hook to verify the user with reCAPTCHA
  const { getRecaptchaAPIToken } = useRecaptchaVerification()

  /**
   * Get the user info from current logged-in user
   * Create a JWT token for Coral Talk
   */
  async function getUserInfo(): Promise<UserInfo> {
    // Get the current user
    const user: User = auth.currentUser

    // Return the user info and the JWT token
    return {
      user: user,
      jwt_token: user
        ? await getToken(user, await getRecaptchaAPIToken())
        : null,
    }
  }

  useEffect(() => {
    // Wait for firebase auth to be initialized
    const unsubscribe = onAuthStateChanged(auth, async (): Promise<void> => {
      // Refresh the page when the user is logged in or logged out
      if (hasMounted.current) {
        // Check if the Drawer is opened in local storage
        const commentsOpened: string = localStorage.getItem('commentsOpened')

        if (commentsOpened) {
          // Save the Drawer state to local storage
          localStorage.setItem('commentsOpenedRedirect', 'true')
        }
      } else {
        // The component is mounted, set the flag to true
        hasMounted.current = true
        // The first time the component is mounted, load Coral Talk
        loadCoralTalk()
      }
    })

    // Clean up the subscription
    return () => unsubscribe()
  }, [])

  /**
   * Load Coral Talk script from server, and create the embed comment system
   */
  async function loadCoralTalk(): Promise<void> {
    // Check if the document is available
    if (!document) return

    // Get the user info
    const userInfo: UserInfo = await getUserInfo()

    // Get the document and create a script tag
    const d: Document = document
    const s: HTMLScriptElement = d.createElement('script')
    s.src = `${URL}/assets/js/embed.js`
    s.async = false
    s.defer = true
    s.id = 'coral_talk_script'
    s.crossOrigin = 'anonymous'
    s.onload = () => {
      // @ts-ignore
      Coral.createStreamEmbed({
        id: element_id,
        // The full story ID, using category and storyID, used to identify the content in coral talk
        storyID: getStoryID(category, storyID),
        autoRender: true,
        amp: true,
        rootURL: URL,
        // The JWT token of the user
        accessToken: userInfo.jwt_token ? userInfo.jwt_token : null,
        customCSSURL: '/coral/style.css',

        events: (events) => {
          // Add an event listener for the login prompt
          events.on('loginPrompt', () => {
            // Open a popup for login instead of redirecting
            const loginWindow = window.open(
              '/login',
              'loginWindow',
              'width=800,height=600,center=1',
            )

            // Function to handle the message from the login popup
            function handleLogin(message) {
              if (message.data === 'loginSuccess') {
                // Close the login popup
                loginWindow.close()

                if (!window) return

                // Remove the event listener
                window.removeEventListener('message', handleLogin)

                // Redirect to the new URL with openDrawer parameter
                window.location.href = window.location.href.includes('?')
                  ? `${window.location.href}&comments=true`
                  : `${window.location.href}?comments=true`
              }
            }

            if (!window) return

            // Add event listener for the message from the popup
            window.addEventListener('message', handleLogin)
          })
        },
      })
    }
    ;(d.head || d.body).appendChild(s)
  }

  return (
    <>
      <div id="comments-loaded" style={{ display: 'none' }} />
      <div className={className}>
        <div id={element_id} ref={ref} />
      </div>
    </>
  )
}

export default CommentBox
