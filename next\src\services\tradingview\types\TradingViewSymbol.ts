export interface TradingViewSymbol {
  symbol: string
  'symbol-fullname'?: string
  description?: string
  isin?: string | null
  'isin-displayed'?: string | null
  root?: string
  'exchange-listed'?: string
  sedol?: string | null
  'currency-id'?: string
  'exchange-traded'?: string
  'base-currency-id'?: string | null
  country?: string
  mic?: string
  'figi-exchange-level'?: string | null
  'symbol-type'?: string
  typespecs?: string
  'option-style'?: string | null
  'kind-available-without-permission'?: string
}
