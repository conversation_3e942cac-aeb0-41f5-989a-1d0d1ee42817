import clsx from 'clsx'
import dayjs from 'dayjs'
import { type FC, Suspense, useCallback } from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import GoldIndicesRatios from '~/src/components/GoldIndicesRatios/GoldIndicesRatios'
import GoldRatiosTable from '~/src/components/GoldRatiosTable/GoldRatiosTable'
import GoldRatiosTableMobile from '~/src/components/GoldRatiosTable/GoldRatiosTableMobile'
import { Query } from '~/src/components/Query/Query'
import type { GoldRatiosQuery } from '~/src/generated'
import type { KitcoGoldRatiosTableData } from '~/src/hooks/kitco-proxy/useKitcoGoldRatiosTable'
import { useKitcoGoldRatiosTable } from '~/src/hooks/kitco-proxy/useKitcoGoldRatiosTable'
import { metals } from '~/src/lib/metals-factory.lib'
import * as timestamps from '~/src/utils/timestamps'

const GoldRatiosCell: FC = () => {
  const fetcher = metals.goldRatios({
    variables: {
      timestamp: timestamps.current(),
      symbols: '$XAU,$HUI,$SPX,$DOWI',
    },
    options: {
      select: useCallback((d: GoldRatiosQuery) => {
        const transformCrudeOilName = d?.crudeOil?.results?.filter(
          (item: any) => item.name === 'Crude Oil WTI',
        )
        return {
          ...d,
          crudeOil: { ...d.crudeOil, results: transformCrudeOilName },
        }
      }, []),
    },
  })

  // Get live Kitco data for XAU, HUI, SPX, DJI, CL
  const { data: kitcoData } = useKitcoGoldRatiosTable()

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {(res) => {
            // Merge existing data with live Kitco data
            const mergedRatios = calculateRatiosWithKitco(res.data, kitcoData)

            return (
              <>
                <div className={clsx('hidden desktop:block')}>
                  <GoldRatiosTable data={mergedRatios} />
                </div>
                <div className={clsx('block desktop:hidden')}>
                  <GoldRatiosTableMobile data={mergedRatios} />
                  <GoldIndicesRatios data={mergedRatios} />
                </div>
              </>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export default GoldRatiosCell

// HELPERS
export function calculateRatios(data?: GoldRatiosQuery) {
  if (!data) return undefined

  const numerator = data?.gold?.results[0]?.bid

  const quoteFinder = (s: string) =>
    data?.quotes?.results?.find(({ symbol }) => symbol === s)?.lastPrice

  const crudeOilFinder = () =>
    data?.crudeOil?.results?.find(
      ({ symbol }) => symbol === getSymbolCurrentCrudeOil(),
    )?.lastPrice

  const values = {
    silver: numerator / data?.silver?.results[0]?.bid,
    platinum: numerator / data?.platinum?.results[0]?.bid,
    palladium: numerator / data?.palladium?.results[0]?.bid,
    xau: numerator / quoteFinder('$XAU'),
    hui: numerator / quoteFinder('$HUI'),
    spx: numerator / quoteFinder('$SPX'),
    dowi: numerator / quoteFinder('$DOWI'),
    crudeOil: numerator / crudeOilFinder(),
  }

  return values
}

// give ross headache to find the absolutely arbitrary ratio
// TODO: this whole query should prob be calculated on the server
// function findAbsolutelyArbitraryRatio(data?: GetGoldRatiosQuery) {
//   if (!data) return 0
//   return data?.crudeOil?.results?.filter((x) => x.name !== 'Crude Oil WTI')
// }

export type CalculatedRatios = ReturnType<typeof calculateRatios>

/**
 * Enhanced calculate ratios function that merges existing data with live Kitco data
 * Uses live Kitco data for XAU, HUI, SPX, DJI, CL when available,
 * falls back to existing data for silver, platinum, palladium, and as backup
 */
export function calculateRatiosWithKitco(
  data?: GoldRatiosQuery,
  kitcoData?: KitcoGoldRatiosTableData,
) {
  // Start with existing ratios as fallback
  const existingRatios = calculateRatios(data)

  if (!kitcoData || !data) {
    // If no Kitco data or no base data, return existing ratios
    return existingRatios
  }

  // Get the proper gold bid price as numerator (same as original calculation)
  const goldBid = data?.gold?.results[0]?.bid

  if (!goldBid) {
    // If no gold bid price, return existing ratios
    return existingRatios
  }

  // Calculate ratios using proper gold bid as numerator and live Kitco prices as denominators
  // Formula: Gold Bid / [commodity or Index price from Kitco]
  const mergedRatios = {
    // Keep existing data for metals not available in Kitco
    silver: existingRatios?.silver,
    platinum: existingRatios?.platinum,
    palladium: existingRatios?.palladium,

    // Use live Kitco data prices for ratio calculations, fallback to existing
    xau: kitcoData.xau?.price
      ? goldBid / kitcoData.xau.price
      : existingRatios?.xau,
    hui: kitcoData.hui?.price
      ? goldBid / kitcoData.hui.price
      : existingRatios?.hui,
    spx: kitcoData.spx?.price
      ? goldBid / kitcoData.spx.price
      : existingRatios?.spx,
    dowi: kitcoData.dji?.price
      ? goldBid / kitcoData.dji.price
      : existingRatios?.dowi,
    crudeOil: kitcoData.cl?.price
      ? goldBid / kitcoData.cl.price
      : existingRatios?.crudeOil,
  }

  return mergedRatios
}

const getSymbolCurrentCrudeOil = () => {
  const currentDate = dayjs()

  const nextMonthDate = currentDate.add(1, 'month')

  // the letter corresponding to the month
  // refs: https://favish.atlassian.net/browse/KTCR-795?focusedCommentId=59437
  const symbols = 'FGHJKMNQUVXZ'

  const nextActuallyMonth = symbols[nextMonthDate.month()]

  return `CL${nextActuallyMonth}${nextMonthDate.format('YY')}`
}
