import { elasticClient } from '../client/elasticsearch'
import { TradingViewSymbol } from '../types/TradingViewSymbol'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Finds a symbol by exact match (case-insensitive).
 */
export async function findSymbol(
  symbol: string,
): Promise<TradingViewSymbol | undefined> {
  try {
    const response = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1,
      query: {
        term: { symbol: symbol.toLowerCase() },
      },
    })

    if (response.hits.hits.length > 0) {
      return response.hits.hits[0]._source as TradingViewSymbol
    }

    return undefined
  } catch (error) {
    console.error('Error finding symbol in Elasticsearch:', error)
    return undefined
  }
}
