import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import LeftContent from '~/src/components/CommodityContentDetailChart/LeftContent'
import RightContent from '~/src/components/CommodityContentDetailChart/RightContent'
import cs from '~/src/utils/cs'
import { TradingViewChartWidget } from '../TradingViewChartWidget/TradingViewChartWidget'
import styles from './CommodityContentDetailChart.module.scss'

interface CommodityContentDetailChartProps {
  name: string
  symbol: string
  ssrTimestamp: number
  labelComponent?: React.ReactNode
  hideLatestNewsHeader?: boolean
  isBaseMetal?: boolean
}

const CommodityContentDetailChart: FC<CommodityContentDetailChartProps> = ({
  name,
  symbol,
  ssrTimestamp,
  labelComponent,
  hideLatestNewsHeader,
  isBaseMetal,
}: CommodityContentDetailChartProps) => {
  return (
    <div className={cs(['block md:gap-[15px]', styles.tabletGridOrder])}>
      <LeftContent
        name={name}
        symbol={symbol}
        ssrTimestamp={ssrTimestamp}
        isBaseMetal={isBaseMetal}
      />
      {/* Main content */}
      <div className="md:mt-[34px] lg:mt-0">
        <div className="relative mb-3 h-[500px]">
          {labelComponent}
          <TradingViewChartWidget symbol={`X${symbol}USD`} />
        </div>

        <div className="mt-[30px]">
          <AdvertisingSlot
            id={'oop'}
            className={
              'mx-auto mb-[15px] flex min-h-[250px] min-w-[300px] justify-center desktop:min-h-[384px]'
            }
          />
        </div>
      </div>
      {/* End main content */}
      <RightContent
        classNames="block md:hidden desktop:block md:mt-[34px] lg:mt-0"
        hideLatestNewsHeader={hideLatestNewsHeader}
      />
    </div>
  )
}

export default CommodityContentDetailChart
