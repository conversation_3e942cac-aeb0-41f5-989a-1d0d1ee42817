import dynamic from 'next/dynamic'
import type { FC } from 'react'

// Import TradingView widget dynamically for client-side rendering only
const TradingViewWidget = dynamic(
  () => import('~/src/services/tradingview/components/TradingViewWidget'),
  { ssr: false },
)

interface TradingViewMarketIndicesProps {
  className?: string
}

/**
 * TradingView-based market indices component that replaces MarketPageIndicesCell
 * Shows major market indices in a compact format
 */
const TradingViewMarketIndices: FC<TradingViewMarketIndicesProps> = ({
  className,
}) => {
  const handleError = (error: string) => {
    console.error('TradingView market indices error:', error)
  }

  return (
    <div className={className}>
      <div className="mb-4">
        <h2 className="text-xl font-semibold">Market Indices</h2>
      </div>
      <div className="bg-white border border-gray-200 rounded-md overflow-hidden">
        <TradingViewWidget
          symbol="TVC:DJI" // Default to <PERSON> Jones
          onError={handleError}
          theme="light"
          autosize={false}
          height={550}
          interval="D"
          timezone="Etc/UTC"
          style="1"
          locale="en"
          toolbar_bg="#f1f3f6"
          enable_publishing={false}
          withdateranges={false}
          hide_side_toolbar={true}
          allow_symbol_change={true}
          save_image={false}
          show_popup_button={false}
        />
      </div>
    </div>
  )
}

export default TradingViewMarketIndices
