/**
 * CSV URL mapping utility for replacing old URLs with new ones
 * based on the bc_to_tv_urls.csv file
 */

let urlMappingCache: Map<string, string> | null = null

/**
 * Loads and parses the CSV URL mapping file
 * Returns a Map where keys are old URLs and values are new URLs
 */
async function loadUrlMappingFromCSV(): Promise<Map<string, string>> {
  if (urlMappingCache) {
    return urlMappingCache
  }

  try {
    let csvContent: string

    // Check if we're in a server environment
    if (typeof window === 'undefined') {
      // Server-side: use fs to read the file
      const fs = await import('fs')
      const path = await import('path')

      const csvPath = path.join(process.cwd(), 'next/data/bc_to_tv_urls.csv')
      csvContent = fs.readFileSync(csvPath, 'utf-8')
    } else {
      // Client-side: fetch the CSV file from internal API
      console.log('📡 Fetching CSV from internal API...')
      const response = await fetch('/api/internal/csv-url-mapping')
      if (!response.ok) {
        console.error(
          `Failed to fetch CSV from internal API: ${response.status} ${response.statusText}`,
        )
        throw new Error(`Failed to fetch CSV: ${response.statusText}`)
      }
      csvContent = await response.text()
      console.log('📄 CSV fetched successfully, length:', csvContent.length)
    }

    const mapping = new Map<string, string>()

    // Split into lines and process each line
    const lines = csvContent.split('\n')
    console.log('🔄 Processing CSV lines:', lines.length)

    let processedCount = 0
    for (const line of lines) {
      const trimmedLine = line.trim()
      if (!trimmedLine) continue

      // Split by semicolon to get old and new URLs
      const [oldUrl, newUrl] = trimmedLine.split(';')

      if (oldUrl && newUrl) {
        mapping.set(oldUrl.trim(), newUrl.trim())
        processedCount++
      }
    }

    console.log(
      '✅ CSV parsing completed. Processed:',
      processedCount,
      'mappings',
    )

    // Test if BIGT.VN mapping exists
    const testUrl = 'https://www.kitco.com/markets/stocks/BIGT.VN'
    const testMapping = mapping.get(testUrl)
    console.log('🧪 Test mapping for BIGT.VN:', testMapping || 'NOT FOUND')

    urlMappingCache = mapping
    return mapping
  } catch (error) {
    console.error('Error loading CSV URL mapping:', error)
    return new Map()
  }
}

/**
 * Gets the base URL from environment variables
 */
function getBaseUrl(): string {
  return process.env.NEXT_PUBLIC_URL || 'http://localhost:3000'
}

/**
 * Replaces a URL based on the CSV mapping
 * Returns the new URL with hostname prepended if found in mapping, otherwise returns the original URL
 */
export async function replaceUrlFromMapping(
  originalUrl: string,
): Promise<string> {
  if (!originalUrl) return originalUrl

  const mapping = await loadUrlMappingFromCSV()
  const mappedUrl = mapping.get(originalUrl)

  if (mappedUrl) {
    const baseUrl = getBaseUrl()
    // Remove any leading slash from mappedUrl to avoid double slashes
    const cleanMappedUrl = mappedUrl.startsWith('/')
      ? mappedUrl.slice(1)
      : mappedUrl
    return `${baseUrl}/${cleanMappedUrl}`
  }

  return originalUrl
}

/**
 * Synchronous version for client-side usage (requires mapping to be pre-loaded)
 */
export function replaceUrlFromMappingSync(originalUrl: string): string {
  if (!originalUrl) return originalUrl

  // Only show debug for the first few URLs to avoid console spam
  if (typeof window !== 'undefined' && originalUrl.includes('BIGT.VN')) {
    console.log('🔍 URL Mapping Debug for BIGT.VN:', {
      originalUrl,
      cacheExists: !!urlMappingCache,
      cacheSize: urlMappingCache?.size || 0,
    })
  }

  if (!urlMappingCache) {
    console.warn('⚠️ URL mapping cache not loaded yet for:', originalUrl)
    return originalUrl
  }

  const mappedUrl = urlMappingCache.get(originalUrl)

  if (mappedUrl) {
    const baseUrl = getBaseUrl()
    // Remove any leading slash from mappedUrl to avoid double slashes
    const cleanMappedUrl = mappedUrl.startsWith('/')
      ? mappedUrl.slice(1)
      : mappedUrl
    const finalUrl = `${baseUrl}/${cleanMappedUrl}`

    // Only show success message for BIGT.VN to avoid console spam
    if (typeof window !== 'undefined' && originalUrl.includes('BIGT.VN')) {
      console.log('✅ URL replaced:', originalUrl, '→', finalUrl)
    }

    return finalUrl
  }

  return originalUrl
}

/**
 * Pre-loads the URL mapping for synchronous usage
 * Should be called during application initialization
 */
export async function preloadUrlMapping(): Promise<void> {
  try {
    console.log('🚀 Starting URL mapping preload...')
    await loadUrlMappingFromCSV()
    console.log(
      '✅ URL mapping preload completed. Cache size:',
      urlMappingCache?.size || 0,
    )
  } catch (error) {
    console.error('❌ Failed to preload URL mapping:', error)
  }
}

/**
 * Gets the current mapping cache (for debugging/testing purposes)
 */
export function getUrlMappingCache(): Map<string, string> | null {
  return urlMappingCache
}
