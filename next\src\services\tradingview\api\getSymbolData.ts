/**
 * Get symbol data via API
 * @param symbol Symbol to get data for
 * @returns Promise resolving to symbol data or null
 */
export async function getSymbolData(symbol: string): Promise<any> {
  try {
    const response = await fetch(
      `/api/symbol/verify?symbol=${encodeURIComponent(symbol)}`,
    )

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.symbolData || null
  } catch (error) {
    console.error('Symbol data API error:', error)
    return null
  }
}
