import dayjs from 'dayjs'
import type { FC } from 'react'
import Icon from '~/src/components/Icon/Icon'
import { Socials } from '~/src/components/socials/Socials'
import type { BarchartsQuotesQuery } from '~/src/generated'
import cs from '~/src/utils/cs'
import styles from './StockDetailTitleBlock.module.scss'

interface StockDetailTitleBlockProps {
  data?: BarchartsQuotesQuery
  symbolData?: any
  symbol?: string
}

/**
 * StockDetailTitleBlock is a component that displays stock/market symbol details
 * It can work with either Barchart data or Elasticsearch data
 */
const StockDetailTitleBlock: FC<StockDetailTitleBlockProps> = ({
  data,
  symbolData,
  symbol,
}) => {
  // Handle loading state
  if (!data && !symbolData) {
    return <h2 className="animate-loading h-[48px] w-[300px] !bg-black/10"></h2>
  }

  const { format } = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  })

  // Check if we're using elastic data or barchart data
  const usingElasticData = !!symbolData

  // Determine direction of price change (up or down)
  const isUp = () => {
    if (data?.GetBarchartQuotes?.results) {
      for (const x of data.GetBarchartQuotes.results) {
        const stringifyValue = x.netChange.toString()
        if (stringifyValue.charAt(0) === '-') {
          return false
        }
        return true
      }
    }
    // For placeholder data, we'll use a consistent direction
    return placeholderDirection === 'up'
  }

  // Get title and price details from either data source
  let title: string = ''
  let price: string | null = null
  let netChange: string | null = null
  let percentChange: string | null = null
  let currency: string = 'USD'
  // For placeholder data
  let placeholderDirection: 'up' | 'down' = 'up'

  if (usingElasticData) {
    // Use Elasticsearch data - prefer description field for title as it has full company name
    title =
      symbolData.description || symbolData['symbol-fullname'] || symbol || ''

    // Get currency from symbolData if available
    if (symbolData['currency-id']) {
      currency = symbolData['currency-id']
    }

    // TODO: Replace with actual TradingView API data when available
    // For now, use placeholder/mock data for price information based on symbol
    // Using the symbol as a seed to generate consistent values for the same symbol
    const symbolSeed =
      symbol?.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) || 0

    // Generate a price between $5 and $200 based on the symbol
    const basePrice = 5 + (symbolSeed % 195)
    // Add some decimals for realism
    const priceWithDecimals = basePrice + (symbolSeed % 100) / 100

    // Determine if the change is positive or negative
    placeholderDirection = symbolSeed % 2 === 0 ? 'up' : 'down'
    const changeMultiplier = placeholderDirection === 'up' ? 1 : -1

    // Generate a reasonable change amount (0.01 to 5% of the price)
    const baseChange =
      priceWithDecimals * (0.01 + (symbolSeed % 5) / 100) * changeMultiplier
    const percentValue = (baseChange / priceWithDecimals) * 100

    price = format(priceWithDecimals)
    netChange = baseChange.toFixed(2)
    percentChange = `(${percentValue.toFixed(2)}%)`
  } else if (data?.GetBarchartQuotes?.results?.length > 0) {
    // Use Barchart data
    const index = data.GetBarchartQuotes.results[0]
    title = index?.name || data.GetBarchartQuotes.symbols || ''

    if (index?.lastPrice) {
      price = format(index.lastPrice)
      netChange = index.netChange.toFixed(2)
      percentChange = `(${index.percentChange}%)`
    }
  }

  const styleUpOrDown = !isUp() ? styles.down : styles.up
  const dateLabel = dayjs().format('MMM D, YYYY')

  return (
    <div>
      <div className={styles.titleContainer}>
        <h1 className="font-semibold sm:text-3xl lg:text-5xl">{title}</h1>
        <Socials
          email={null}
          facebook={'KitcoNews'}
          linkedIn={'company/kitconews'}
          twitter={'KitcoNewsNOW'}
        />
      </div>

      <h1 className="font-semibold sm:text-3xl lg:text-5xl">
        {price}
        <span className="ml-4 text-sm font-normal text-gray-600">LAST</span>
        {usingElasticData && !data && (
          <span className="ml-2 text-xs text-gray-500">(Demo price)</span>
        )}
      </h1>

      <div className="mt-2 flex divide-x divide-gray-400 text-base">
        <h3 className="pr-2 font-semibold">{currency}</h3>
        <span className={cs([styleUpOrDown, 'pl-2 pr-2'])}>
          <Icon
            icon={!isUp() ? 'arrow-down' : 'arrow-up'}
            color={!isUp() ? 'red' : 'green'}
            size="16px"
            margin="0px 6px 2px 0"
          />
          {netChange}
          &nbsp; {percentChange}
          {usingElasticData && !data && (
            <span className="ml-1 text-xs text-gray-500">(Demo)</span>
          )}
        </span>
        <span className="pl-2">{dateLabel}</span>
      </div>
    </div>
  )
}

export default StockDetailTitleBlock
