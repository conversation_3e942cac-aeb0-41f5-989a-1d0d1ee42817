import type { KitcoValue } from '../types/KitcoValue'
import { parseXmlResponse } from '../utils/parseXmlResponse'

/**
 * Gold indicators symbols for the Mid Right Rail
 */
export const GOLD_INDICATORS_SYMBOLS = ['XAU', 'HUI', 'SPTTGD'] as const

/**
 * Fetches gold indicators data using the multi-symbol compact format endpoint
 * URL: https://proxy.kitco.com/getValue?ver=2.0&symbol=XAU,HUI,SPTTGD&type=xml
 *
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to array of gold indicators data
 */
export async function fetchGoldIndicatorsData(
  version = '2.0',
): Promise<KitcoValue[]> {
  try {
    // Build the Kitco proxy URL directly
    const symbols = GOLD_INDICATORS_SYMBOLS.join(',')
    const url = `https://proxy.kitco.com/getValue?ver=${version}&symbol=${symbols}&type=xml`

    // Fetch data directly from Kitco proxy
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'text/plain, text/xml',
        'User-Agent': 'Kitco-CMS-Next/1.0',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`)
    }

    // Get the XML response as text
    const responseText = await response.text()
    console.log('Raw response from Kitco proxy:', responseText)

    if (!responseText) {
      throw new Error('Empty response from API')
    }

    // Parse the XML response
    const parsedResponse = await parseXmlResponse(responseText)
    console.log('Parsed gold indicators data:', parsedResponse.values)

    if (!parsedResponse.values || parsedResponse.values.length === 0) {
      throw new Error('Failed to parse any data from XML response')
    }

    // Filter to only the symbols we want (XAU, HUI, SPTTGD)
    const goldIndicatorsData = parsedResponse.values.filter((value) =>
      GOLD_INDICATORS_SYMBOLS.includes(value.symbol as any),
    )

    if (goldIndicatorsData.length === 0) {
      throw new Error('No matching symbols found in response')
    }

    return goldIndicatorsData
  } catch (error) {
    console.error('Error fetching gold indicators data:', error)
    console.error(
      'Kitco URL attempted:',
      `https://proxy.kitco.com/getValue?ver=${version}&symbol=${GOLD_INDICATORS_SYMBOLS.join(',')}&type=xml`,
    )

    // More detailed error logging
    if (
      error instanceof TypeError &&
      error.message.includes('Failed to fetch')
    ) {
      throw new Error('Network error - unable to reach Kitco proxy')
    } else if (error instanceof Error) {
      throw new Error(`API Error: ${error.message}`)
    } else {
      throw new Error('Unknown error occurred while fetching data')
    }
  }
}

/**
 * Fetches a specific symbol from the gold indicators data
 *
 * @param symbol - Symbol to fetch (XAU, HUI, or SPTTGD)
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to specific symbol data or null if not found
 */
export async function fetchGoldIndicatorBySymbol(
  symbol: 'XAU' | 'HUI' | 'SPTTGD',
  version = '2.0',
): Promise<KitcoValue | null> {
  const allData = await fetchGoldIndicatorsData(version)
  return allData.find((item) => item.symbol === symbol) || null
}

/**
 * Test function to verify XML parsing logic
 * This can be called from browser console for debugging
 */
export async function testGoldIndicatorsParsing() {
  // Known XML response format from Kitco proxy
  const testResponse = `<?xml version='1.0' encoding='UTF-8'?>
<Values>
  <Value>
    <Symbol>XAU</Symbol>
    <Timestamp>2025-07-30 14:00:00</Timestamp>
    <Price>209.20</Price>
    <Change>-1.93</Change>
    <ChangePercentage>-0.91</ChangePercentage>
  </Value>
  <Value>
    <Symbol>HUI</Symbol>
    <Timestamp>2025-07-30 14:00:00</Timestamp>
    <Price>431.83</Price>
    <Change>-4.79</Change>
    <ChangePercentage>-1.10</ChangePercentage>
  </Value>
  <Value>
    <Symbol>SPTTGD</Symbol>
    <Timestamp>2025-07-30 14:00:00</Timestamp>
    <Price>513.36</Price>
    <Change>-4.54</Change>
    <ChangePercentage>-0.88</ChangePercentage>
  </Value>
</Values>`

  console.log('Testing XML parsing with known response:', testResponse)

  try {
    const parsed = await parseXmlResponse(testResponse)
    console.log('✅ XML parsing successful:', parsed)
    return parsed
  } catch (error) {
    console.log('❌ XML parsing failed:', error)
    return null
  }
}

// Make test function available globally for debugging
if (typeof window !== 'undefined') {
  ;(window as any).testGoldIndicatorsParsing = testGoldIndicatorsParsing
}
