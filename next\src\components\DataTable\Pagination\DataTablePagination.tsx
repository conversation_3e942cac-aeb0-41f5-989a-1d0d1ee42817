import type { Table } from '@tanstack/table-core'
import clsx from 'clsx'
import { useEffect, useMemo, useState } from 'react'
import { FaAngleDoubleLeft, FaAngleDoubleRight } from 'react-icons/fa'
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa6'

interface DataTablePaginationProps<TData> {
  table: Table<TData>
  showPageNumbers?: boolean
  setPageIndex?: (pageIndex: number) => void
  setPageSize?: (pageSize: number) => void
}

const DataTablePagination = <TData,>({
  table,
  showPageNumbers = true,
  setPageIndex,
  setPageSize,
}: DataTablePaginationProps<TData>) => {
  // State for the current page
  const [currentPage, setCurrentPage] = useState(0)

  // State for the total pages
  const [totalPages, setTotalPages] = useState(0)

  // Update the current page when the table state changes
  useEffect(() => {
    setCurrentPage(table.getState().pagination.pageIndex + 1)
  }, [table.getState().pagination.pageIndex])

  // Update the total pages when the table state changes
  useEffect(() => {
    setTotalPages(table.getPageCount())
  }, [table.getPageCount()])

  // Generate the page numbers
  const pages = useMemo(() => {
    let pageNumbers: (number | string)[] = []
    const showPages = 2

    if (totalPages <= 7) {
      pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1)
    } else {
      const firstPages = Array.from({ length: showPages }, (_, i) => i + 1)
      const lastPages = Array.from(
        { length: showPages },
        (_, i) => totalPages - showPages + 1 + i,
      )

      if (currentPage <= showPages) {
        pageNumbers = [...firstPages, '...', ...lastPages]
      } else if (currentPage >= totalPages - showPages) {
        pageNumbers = [1, '...', ...lastPages]
      } else {
        pageNumbers = [
          1,
          '...',
          currentPage - 1,
          currentPage,
          currentPage + 1,
          '...',
          totalPages,
        ]
      }
    }

    return pageNumbers
  }, [currentPage, totalPages])

  // If there are no pages, return nothing
  if (table.getPageCount() <= 0) {
    return <></>
  }

  return (
    <div>
      <div className="flex items-center justify-between gap-2 p-4 bg-gray-100 rounded-lg shadow-md w-full overflow-x-auto">
        <button
          type="button"
          className="border rounded p-2 text-gray-500 hover:bg-gray-200 disabled:opacity-50"
          onClick={() => table.firstPage()}
          disabled={!table.getCanPreviousPage()}
        >
          <FaAngleDoubleLeft />
        </button>
        <button
          type="button"
          className="border rounded p-2 text-gray-500 hover:bg-gray-200 disabled:opacity-50"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          <FaAngleLeft />
        </button>
        {showPageNumbers && (
          <div className="flex items-center gap-1">
            {pages.map((page) => (
              <button
                key={
                  typeof page === 'number'
                    ? `page-${page}`
                    : `ellipsis-${Math.random()}`
                }
                type="button"
                className={clsx(
                  'border rounded p-2',
                  page === table.getState().pagination.pageIndex + 1
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500',
                  page !== '...' && 'hover:bg-gray-200',
                  page === '...' && 'cursor-default',
                )}
                onClick={() =>
                  page !== '...' && setPageIndex((page as number) - 1)
                }
                disabled={page === '...'}
              >
                {page}
              </button>
            ))}
          </div>
        )}
        <button
          type="button"
          className="border rounded p-2 text-gray-500 hover:bg-gray-200 disabled:opacity-50"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          <FaAngleRight />
        </button>
        <button
          type="button"
          className="border rounded p-2 text-gray-500 hover:bg-gray-200 disabled:opacity-50"
          onClick={() => table.lastPage()}
          disabled={!table.getCanNextPage()}
        >
          <FaAngleDoubleRight />
        </button>
      </div>
      <div className="flex items-center justify-between gap-2 p-4 bg-gray-100 rounded-lg">
        <span className="flex items-center gap-1 flex-wrap">
          <div className="text-gray-600">Page</div>
          <strong className="text-gray-800">
            {table.getState().pagination.pageIndex + 1} of{' '}
            {table.getPageCount().toLocaleString()}
          </strong>{' '}
          <div className="text-gray-600 text-sm">
            ({table.getRowCount().toLocaleString()} Companies)
          </div>
        </span>
        <select
          value={table.getState().pagination.pageSize}
          onChange={(e) => {
            setPageSize(Number(e.target.value))
          }}
          className="border p-1 rounded bg-white"
        >
          {[10, 20, 30, 40, 50, 100].map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              Show {pageSize}
            </option>
          ))}
        </select>
      </div>
    </div>
  )
}

export default DataTablePagination
