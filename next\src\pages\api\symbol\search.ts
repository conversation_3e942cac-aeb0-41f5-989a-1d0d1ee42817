import type { NextApiRequest, NextApiResponse } from 'next'
import { searchSymbolsInElastic } from '~/src/services/tradingview/utils/searchSymbolsInElastic'

type ResponseData = {
  results: any[]
  error?: string
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData>,
) {
  // Set no-cache headers to prevent caching of search results
  res.setHeader(
    'Cache-Control',
    'no-cache, no-store, max-age=0, must-revalidate',
  )
  res.setHeader('Pragma', 'no-cache')
  res.setHeader('Expires', '0')

  if (req.method !== 'GET') {
    return res.status(405).json({ results: [], error: 'Method not allowed' })
  }

  const { query, limit = '10' } = req.query

  if (!query || typeof query !== 'string') {
    return res
      .status(400)
      .json({ results: [], error: 'Search query is required' })
  }

  try {
    // Search for symbols in Elasticsearch
    const results = await searchSymbolsInElastic(
      query,
      parseInt(limit as string, 10),
    )

    return res.status(200).json({ results })
  } catch (error) {
    console.error('Error searching symbols:', error)
    return res.status(500).json({ results: [], error: 'Internal server error' })
  }
}
