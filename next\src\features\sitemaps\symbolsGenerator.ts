import { saveFileToGCS } from '~/src/services/google-cloud/storage'
import { TradingViewSymbol } from '~/src/services/tradingview/types/TradingViewSymbol'
import {
  clearScroll,
  getAllSymbolsFromElastic,
  getSymbolsCount,
} from '~/src/services/tradingview/utils/getAllSymbolsFromElastic'
import { createXMLSitemap } from './generator'

const baseURL = process.env.NEXT_PUBLIC_URL

/**
 * Generate URL object for a symbol
 * @param symbol TradingView symbol data
 * @returns URL object for sitemap
 */
function generateSymbolUrlObject(symbol: TradingViewSymbol): { loc: string } {
  const symbolPath = symbol['symbol-fullname'] || symbol.symbol
  return {
    loc: `${baseURL}/markets/stocks/${symbolPath}`,
  }
}

/**
 * Generate symbols sitemap XML content
 * @param symbols Array of TradingView symbols
 * @returns XML sitemap content
 */
function generateSymbolsSitemap(symbols: TradingViewSymbol[]): string {
  const urlObjects = symbols
    .filter((symbol) => symbol.symbol) // Ensure symbol exists
    .map(generateSymbolUrlObject)

  return createXMLSitemap(urlObjects, 'symbols')
}

/**
 * Generate and store symbols sitemaps with batching
 * This function handles large datasets by creating multiple sitemap files
 */
export async function generateSymbolsSitemaps(): Promise<void> {
  try {
    console.log('Starting symbols sitemap generation...')

    // Get total count of symbols
    const totalSymbols = await getSymbolsCount()
    console.log(`Total symbols to process: ${totalSymbols}`)

    if (totalSymbols === 0) {
      console.log('No symbols found, skipping sitemap generation')
      return
    }

    // Configuration for batching
    const BATCH_SIZE = 10000 // Symbols per sitemap file (Google recommends max 50,000 URLs per sitemap)
    const FETCH_SIZE = 1000 // Symbols to fetch per Elasticsearch query

    let sitemapIndex = 1
    let processedSymbols = 0
    let currentScrollId: string | undefined

    // Generate sitemap index file URLs
    const sitemapIndexUrls: string[] = []

    try {
      while (processedSymbols < totalSymbols) {
        console.log(
          `Processing sitemap ${sitemapIndex} (symbols ${processedSymbols + 1}-${Math.min(processedSymbols + BATCH_SIZE, totalSymbols)})`,
        )

        const batchSymbols: TradingViewSymbol[] = []

        // Collect symbols for this batch using scroll API
        while (
          batchSymbols.length < BATCH_SIZE &&
          processedSymbols + batchSymbols.length < totalSymbols
        ) {
          const { symbols, scrollId } = await getAllSymbolsFromElastic(
            FETCH_SIZE,
            currentScrollId,
          )

          if (symbols.length === 0) {
            console.log('No more symbols to fetch, breaking')
            break
          }

          // Update scroll ID for next iteration
          currentScrollId = scrollId

          // Add symbols to current batch, but don't exceed BATCH_SIZE
          const remainingSlots = BATCH_SIZE - batchSymbols.length
          const symbolsToAdd = symbols.slice(0, remainingSlots)
          batchSymbols.push(...symbolsToAdd)

          console.log(
            `Fetched ${symbols.length} symbols, batch now has ${batchSymbols.length} symbols`,
          )
        }

        if (batchSymbols.length === 0) {
          console.log('No symbols in batch, breaking')
          break
        }

        // Generate sitemap for this batch
        const sitemapXML = generateSymbolsSitemap(batchSymbols)
        const filename = `symbols-${sitemapIndex}.xml`

        // Save sitemap to Google Cloud Storage
        await saveFileToGCS(
          sitemapXML,
          `sitemaps/${filename}`,
          'application/xml',
          true,
        )

        // Add to sitemap index
        sitemapIndexUrls.push(`${baseURL}/static-sitemaps/${filename}`)

        console.log(
          `Generated and uploaded ${filename} with ${batchSymbols.length} symbols`,
        )

        processedSymbols += batchSymbols.length
        sitemapIndex++

        // Add a small delay to avoid overwhelming Elasticsearch
        await new Promise((resolve) => setTimeout(resolve, 100))
      }
    } finally {
      // Clean up scroll context
      if (currentScrollId) {
        await clearScroll(currentScrollId)
      }
    }

    // Generate sitemap index file if we have multiple sitemaps
    if (sitemapIndexUrls.length > 1) {
      const sitemapIndexXML = generateSitemapIndex(sitemapIndexUrls)
      await saveFileToGCS(
        sitemapIndexXML,
        'sitemaps/symbols-index.xml',
        'application/xml',
        true,
      )
      console.log(
        `Generated symbols sitemap index with ${sitemapIndexUrls.length} sitemaps`,
      )
    }

    console.log(
      `Symbols sitemap generation completed. Processed ${processedSymbols} symbols in ${sitemapIndex - 1} sitemap(s)`,
    )
  } catch (error) {
    console.error('Error generating symbols sitemaps:', error)
    throw error
  }
}

/**
 * Generate sitemap index XML for multiple symbol sitemaps
 * @param sitemapUrls Array of sitemap URLs
 * @returns XML sitemap index content
 */
function generateSitemapIndex(sitemapUrls: string[]): string {
  const currentDate = new Date().toISOString()

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapUrls
  .map(
    (url) => `  <sitemap>
    <loc>${url}</loc>
    <lastmod>${currentDate}</lastmod>
  </sitemap>`,
  )
  .join('\n')}
</sitemapindex>`
}
