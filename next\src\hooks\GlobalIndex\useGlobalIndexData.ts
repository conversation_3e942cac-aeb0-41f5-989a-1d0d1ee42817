import { useEffect, useState } from 'react'
import commodityCategories from '~/src/data/GoldIndex/CommodityCategories'
import { useKgxData } from '~/src/hooks/GlobalIndex/useKgxData'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import { formatPercentage } from '~/src/utils/Prices/formatPercentage'
import { formatPrice } from '~/src/utils/Prices/formatPrice'

const API_KEY = process.env.NEXT_PUBLIC_KGX_API_KEY || ''
const PM_API_URL =
  `${process.env.NEXT_PUBLIC_KGX_API_URL}/getPM?apikey=${API_KEY}&symbol=AU,AG,PD,PT&type=json` ||
  ''
const BASE_METALS_API_URL =
  `${process.env.NEXT_PUBLIC_KGX_API_URL}/getBM?apikey=${API_KEY}&symbol=CU,AL,NI,ZNC,LEAD&type=json` ||
  ''
const ENERGY_API_URL =
  `${process.env.NEXT_PUBLIC_KGX_API_URL}/getValue?apikey=${API_KEY}&symbol=CL&type=json` ||
  ''

/**
 * Get the global index data (metals, energy, crypto)
 */
const useGlobalIndexData = () => {
  const kgxData = useKgxData()
  const [fallbackMetalData, setFallbackMetalData] = useState<CommodityData[]>(
    [],
  )
  const [fallbackEnergyData, setFallbackEnergyData] =
    useState<CommodityData | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Fetch fallback data when component mounts or kgxData changes
  useEffect(() => {
    const fetchFallbackData = async () => {
      const hasMetalData =
        kgxData?.preciousMetals?.length > 0 || kgxData?.baseMetals?.length > 0
      const hasEnergyData = kgxData?.energy?.length > 0

      if (hasMetalData && hasEnergyData) {
        return // Skip if we already have data
      }

      setIsLoading(true)
      try {
        if (!hasMetalData) {
          try {
            const [preciousMetalsRes, baseMetalsRes] = await Promise.all([
              fetch(PM_API_URL).then((res) => {
                if (!res.ok) throw new Error('Failed to fetch precious metals')
                return res.json()
              }),
              fetch(BASE_METALS_API_URL).then((res) => {
                if (!res.ok) throw new Error('Failed to fetch base metals')
                return res.json()
              }),
            ])

            const metals = [
              ...(preciousMetalsRes?.data || []),
              ...(baseMetalsRes?.data || []),
            ].map(transformApiData)

            setFallbackMetalData(metals)
          } catch (metalError) {
            console.error('Error fetching metal data:', metalError)
            // Don't rethrow, we want to continue to energy and crypto data
          }
        }

        if (!hasEnergyData) {
          try {
            const energyRes = await fetch(ENERGY_API_URL).then((res) => {
              if (!res.ok) throw new Error('Failed to fetch energy data')
              return res.json()
            })
            const energyData = energyRes?.data?.[0]
              ? transformApiData(energyRes.data[0])
              : null
            setFallbackEnergyData(energyData)
          } catch (energyError) {
            console.error('Error fetching energy data:', energyError)
            // Continue even if energy data fails
          }
        }
      } catch (error) {
        console.error('Error fetching fallback data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchFallbackData()
  }, [kgxData])

  // Use fallback data if primary data is missing
  const metalData = [
    ...(kgxData?.preciousMetals ?? []),
    ...(kgxData?.baseMetals ?? []),
    ...(fallbackMetalData ?? []),
  ]

  const energyData = kgxData?.energy?.[0] ?? fallbackEnergyData ?? null
  const cryptoData = kgxData?.crypto ?? []

  // Return empty array only if we're still loading and have no data at all
  if (
    isLoading &&
    metalData.length === 0 &&
    !energyData &&
    cryptoData.length === 0
  ) {
    return []
  }

  return createTableData(metalData, energyData, cryptoData)
}

// Helper function to transform API data to match your CommodityData type
function transformApiData(apiItem: any): CommodityData {
  return {
    commodity: apiItem.name || '',
    lastBid: {
      bid: apiItem.price || 0,
      bidVal: apiItem.price || 0,
      currency: 'USD',
      originalTime: new Date().toISOString(),
    },
    changeDueToUSD: {
      change: apiItem.change || 0,
      changeVal: apiItem.change || 0,
      percentage: apiItem.changePercent || 0,
      percentageVal: apiItem.changePercent || 0,
    },
    changeDueToTrade: {
      change: apiItem.change || 0,
      changeVal: apiItem.change || 0,
      percentage: apiItem.changePercent || 0,
      percentageVal: apiItem.changePercent || 0,
    },
    totalChange: {
      change: apiItem.change || 0,
      changeVal: apiItem.change || 0,
      percentage: apiItem.changePercent || 0,
      percentageVal: apiItem.changePercent || 0,
    },
  }
}

/**
 * Create the table data
 *
 * @param metalData
 * @param energyData
 * @param cryptoData
 */
function createTableData(
  metalData: CommodityData[],
  energyData: CommodityData,
  cryptoData: CommodityData[],
): CommodityData[] {
  const tableData = generateTableData(metalData, energyData, cryptoData)

  return formatData(tableData)
}

/**
 * Generate the table data from the API data
 *
 * @param {CommodityData[]} metalData - The metal data
 * @param {CommodityData} energyData - The energy data
 * @param {CommodityData[]} cryptoData - The crypto data
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 *
 * @returns {CommodityData[]} The table data
 */
const generateTableData = (
  metalData: CommodityData[],
  energyData: CommodityData,
  cryptoData: CommodityData[],
  onlyPreciousMetals?: boolean,
): CommodityData[] => {
  // No need to process the data if there is only precious metals
  if (onlyPreciousMetals) {
    console.log('Only precious metals')
    return metalData
  }

  // // Return an empty array if there is no data
  // if (!metalData || metalData.length <= 0) {
  //   console.log('No metal data')
  //   return []
  // }

  // Create a copy of the metal data to avoid mutating the original
  const result = [...metalData]
  const goldIndex = result.findIndex((entry) => entry.commodity === 'Gold')
  if (goldIndex > 0) {
    const [gold] = result.splice(goldIndex, 1)
    result.unshift(gold)
  }

  // Move Platinum before Palladium
  const platinumIndex = result.findIndex(
    (entry) => entry.commodity === 'Platinum',
  )
  const palladiumIndex = result.findIndex(
    (entry) => entry.commodity === 'Palladium',
  )
  if (
    platinumIndex !== -1 &&
    palladiumIndex !== -1 &&
    platinumIndex > palladiumIndex
  ) {
    const [platinum] = result.splice(platinumIndex, 1)
    result.splice(palladiumIndex, 0, platinum)
  }

  // Move the aluminum entry to immediately after the nickel entry
  const aluminumIndex = result.findIndex(
    (entry) => entry.commodity === 'Aluminum',
  )
  const aluminumData =
    aluminumIndex !== -1 ? result.splice(aluminumIndex, 1)[0] : null
  const nickelIndex = result.findIndex((entry) => entry.commodity === 'Nickel')
  if (nickelIndex !== -1 && aluminumData) {
    result.splice(nickelIndex + 1, 0, aluminumData)
  }

  // Move the zinc entry to immediately after the aluminum entry
  const zincIndex = result.findIndex((entry) => entry.commodity === 'Zinc')
  const zincData = zincIndex !== -1 ? result.splice(zincIndex, 1)[0] : null
  const newAluminumIndex = result.findIndex(
    (entry) => entry.commodity === 'Aluminum',
  )
  if (newAluminumIndex !== -1 && zincData) {
    result.splice(newAluminumIndex + 1, 0, zincData)
  }

  // Add energy data if available
  if (energyData) {
    // Insert the energy entry before the copper entry
    const copperIndex = result.findIndex(
      (entry) => entry.commodity === 'Copper',
    )
    if (copperIndex !== -1) {
      result.splice(copperIndex, 0, energyData)
    } else {
      result.push(energyData)
    }
  }

  // Add crypto data if available
  if (cryptoData && cryptoData.length > 0) {
    result.push(...cryptoData)
  }

  return result
}

/**
 * Formats an array of CommodityData objects by categorizing each commodity
 * and formatting specific nested properties.
 *
 * @param {CommodityData[]} data - The array of CommodityData objects to format.
 * @returns {CommodityData[]} - A new array of CommodityData objects with formatted values.
 */
function formatData(data: CommodityData[]): CommodityData[] {
  if (!data || data.length <= 0) return []

  return data.map((item: CommodityData) => {
    const category = categorizeCommodity(item.commodity)
    const formattedBid = Number(item.lastBid.bid).toFixed(4)
    return {
      ...item,
      lastBid: {
        ...item.lastBid,
        bid: formattedBid,
      },
      changeDueToUSD: {
        ...item.changeDueToUSD,
        change: formatPrice({
          value: item.changeDueToUSD.changeVal,
          category,
        }),
        percentage: formatPercentage({
          value: item.changeDueToUSD.percentageVal,
        }),
      },
      changeDueToTrade: {
        ...item.changeDueToTrade,
        change: formatPrice({
          value: item.changeDueToTrade.changeVal,
          category,
        }),
        percentage: formatPercentage({
          value: item.changeDueToTrade.percentageVal,
        }),
      },
      totalChange: {
        ...item.totalChange,
        change: formatPrice({
          value: item.totalChange.changeVal,
          category,
        }),
        percentage: formatPercentage({ value: item.totalChange.percentageVal }),
      },
    }
  })
}

/**
 * Categorize the commodity based on the category
 *
 * @param {string} commodity - The commodity to categorize
 *
 * @returns {string} The category of the commodity
 */
function categorizeCommodity(commodity: string): string {
  for (const category in commodityCategories) {
    if (commodityCategories[category].includes(commodity)) {
      return category
    }
  }
  return null
}

export { categorizeCommodity, createTableData, useGlobalIndexData }
