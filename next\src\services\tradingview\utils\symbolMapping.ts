/**
 * Maps Barchart symbols to TradingView symbols
 * This mapping is specifically for the indices used in the markets page
 */
export const barchartToTradingViewSymbolMap: Record<string, string> = {
  // Major US Indices - Updated with verified TradingView symbols
  $DOWI: 'TVC:DJI', // Dow Jones Industrial Average
  $NASX: 'TVC:IXIC', // NASDAQ Composite (more commonly used than NDX)
  $SPX: 'TVC:SPX', // S&P 500 (with TVC prefix)
  $NYA: 'TVC:NYA', // NYSE Composite

  // Additional common mappings that might be used
  $DXY: 'TVC:DXY', // US Dollar Index
  $NKY: 'TVC:NI225', // Nikkei 225
  $TXCX: 'TVC:UKX', // FTSE 100
}

/**
 * Converts a Barchart symbol to a TradingView symbol
 * @param barchartSymbol - The Barchart symbol (e.g., '$DOWI')
 * @returns The corresponding TradingView symbol (e.g., 'TVC:DJI')
 */
export function mapBarchartToTradingView(barchartSymbol: string): string {
  const tradingViewSymbol = barchartToTradingViewSymbolMap[barchartSymbol]

  if (!tradingViewSymbol) {
    console.warn(
      `No TradingView symbol mapping found for Barchart symbol: ${barchartSymbol}`,
    )
    // Return the original symbol without the $ prefix as fallback
    return barchartSymbol.replace('$', '')
  }

  return tradingViewSymbol
}

/**
 * Checks if a Barchart symbol has a TradingView mapping
 * @param barchartSymbol - The Barchart symbol to check
 * @returns True if a mapping exists, false otherwise
 */
export function hasTradingViewMapping(barchartSymbol: string): boolean {
  return barchartSymbol in barchartToTradingViewSymbolMap
}
