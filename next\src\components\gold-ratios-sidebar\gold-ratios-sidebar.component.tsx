import type { FC } from 'react'
import { useCallback } from 'react'
import { calculateRatios } from '~/src/components-metals/GoldRatiosCell/GoldRatiosCell'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import type { GoldRatiosQuery } from '~/src/generated'
import { useKitcoXau } from '~/src/hooks/kitco-proxy/useKitcoXau'
import { metals } from '~/src/lib/metals-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import cs from '~/src/utils/cs'
import dates from '~/src/utils/dates'
import priceFormatter from '~/src/utils/priceFormatter'
import * as timestamps from '~/src/utils/timestamps'
import styles from './gold-ratios-sidebar.module.scss'

export const GoldRatiosSidebar: FC = () => {
  // Existing GraphQL data
  const { data } = kitcoQuery(
    metals.goldRatios({
      variables: {
        timestamp: timestamps.current(),
        symbols: '$XAU,$HUI,$SPX,$DOWI',
      },
      options: {
        select: useCallback((d: GoldRatiosQuery) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { crudeOil, ...data } = d
          return {
            ...data,
          }
        }, []),
      },
    }),
  )

  // New Kitco proxy data (XAU only)
  const { data: kitcoData } = useKitcoXau()

  const dataCalculateRatios = calculateRatios(data)

  // Create array with existing ratios plus live XAU price info
  const dataAsArray = [
    // Live XAU price (from Kitco proxy)
    ...(kitcoData?.xau
      ? [
          {
            label: 'XAU (Live Price)',
            value: kitcoData.xau.price,
            isKitcoData: true,
            isPrice: true,
            price: kitcoData.xau.price,
            change: kitcoData.xau.change,
            changePercentage: kitcoData.xau.changePercentage,
          },
        ]
      : []),
    // Existing calculated ratios
    { label: 'XAU', value: dataCalculateRatios?.xau },
    { label: 'Silver', value: dataCalculateRatios?.silver },
    { label: 'Platinum', value: dataCalculateRatios?.platinum },
  ].filter((item) => item.value !== undefined)

  // Determine which timestamp to show (prefer Kitco if available)
  const displayTimestamp = kitcoData?.timestamp
    ? new Date(kitcoData.timestamp).getTime() / 1000
    : data?.gold?.results[0]?.timestamp

  return (
    <BlockShell title="Gold Ratios">
      <div>
        <div
          className="border-b border-ktc-borders py-[6px] text-center text-xs font-bold"
          suppressHydrationWarning
        >
          {dates.fmtUnix(displayTimestamp, 'MMM DD, YYYY h:mm')} NY Time
          {kitcoData?.timestamp && (
            <span className="ml-1 text-green-600">(Live)</span>
          )}
        </div>
      </div>
      {dataAsArray.map((x, idx) => (
        <div
          className={
            !(idx % 2)
              ? styles.indexContainer
              : cs([styles.indexContainer, styles.altBg])
          }
          key={idx}
        >
          <div className={styles.currentChangeFlex}>
            <p className="text-black">
              {x.isPrice ? x.label : `Gold/${x.label} Ratio`}
              {x.isKitcoData && (
                <span className="ml-1 text-xs text-green-600">●</span>
              )}
            </p>
            <div className="text-right">
              <p className="font-semibold text-black">
                {x.isPrice
                  ? `$${priceFormatter(x.value)}`
                  : priceFormatter(x.value)}
              </p>
              {x.isKitcoData && x.changePercentage && (
                <p className="text-xs text-gray-600">
                  Change: {x.change > 0 ? '+' : ''}
                  {x.change?.toFixed(2)}
                  <span
                    className={
                      x.changePercentage >= 0
                        ? 'text-green-600'
                        : 'text-red-600'
                    }
                  >
                    {' '}
                    ({x.changePercentage.toFixed(2)}%)
                  </span>
                </p>
              )}
            </div>
          </div>
        </div>
      ))}
    </BlockShell>
  )
}
