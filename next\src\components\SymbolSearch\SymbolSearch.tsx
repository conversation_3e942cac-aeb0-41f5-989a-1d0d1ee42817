import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { Fa<PERSON><PERSON><PERSON>, Fa<PERSON>pinner, FaTimes } from 'react-icons/fa'

interface SymbolSearchProps {
  placeholder?: string
  maxResults?: number
  onResultClick?: (result: any) => void
  className?: string
  zIndex?: number
}

const SymbolSearch = ({
  placeholder = 'Search symbols...',
  maxResults = 10,
  onResultClick,
  className = '',
  zIndex = 20,
}: SymbolSearchProps) => {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [navigatingSymbol, setNavigatingSymbol] = useState<string | null>(null)
  const [showResults, setShowResults] = useState(false)
  const [error, setError] = useState('')
  const [hasSearched, setHasSearched] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const resultsContainerRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Add refs to track current query and abort controller
  const currentQueryRef = useRef<string>('')
  const abortControllerRef = useRef<AbortController | null>(null)

  // Handle search input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)

    // Clear previous results immediately when query changes
    setResults([])
    setShowResults(false)
    setError('')
    setHasSearched(false)

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
  }

  // Clear search
  const clearSearch = () => {
    setQuery('')
    setResults([])
    setShowResults(false)
    setHasSearched(false)
    setError('')

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        resultsContainerRef.current &&
        !resultsContainerRef.current.contains(event.target as Node) &&
        !searchInputRef.current?.contains(event.target as Node)
      ) {
        setShowResults(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Fetch search results
  useEffect(() => {
    const fetchResults = async () => {
      if (!query || query.length < 2) {
        setResults([])
        setShowResults(false)
        return
      }

      // Update current query ref to track what we're searching for
      currentQueryRef.current = query

      setLoading(true)
      setError('')
      setShowResults(true) // Show results container immediately to display loading state

      try {
        const abortController = new AbortController()
        abortControllerRef.current = abortController

        const response = await fetch(
          `/api/symbol/search?query=${encodeURIComponent(query)}&limit=${maxResults}`,
          { signal: abortController.signal },
        )

        if (!response.ok) {
          throw new Error('Failed to fetch results')
        }

        const data = await response.json()

        // Only update results if this response is for the current query
        // This prevents race conditions where an older request returns after a newer one
        if (currentQueryRef.current === query) {
          setResults(data.results)
          setHasSearched(true)
        }
      } catch (err) {
        // Ignore aborted requests
        if (err instanceof Error && err.name === 'AbortError') {
          return
        }

        console.error('Error searching symbols:', err)

        // Only set error if this was for the current query
        if (currentQueryRef.current === query) {
          setError('Error searching symbols. Please try again.')
        }
      } finally {
        // Only update loading state if this was for the current query
        if (currentQueryRef.current === query) {
          setLoading(false)
        }
        abortControllerRef.current = null
      }
    }

    // Debounce search to avoid excessive API calls
    const debounceTimer = setTimeout(fetchResults, 300)
    return () => {
      clearTimeout(debounceTimer)
      // Cancel any ongoing request when effect cleans up
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
        abortControllerRef.current = null
      }
    }
  }, [query, maxResults])

  // Check if symbol contains the query (for highlighting)
  const highlightMatch = (text: string) => {
    if (!query || !text) return text

    // For case-insensitive matching
    const queryLower = query.toLowerCase()
    const textLower = text.toLowerCase()

    if (textLower.includes(queryLower)) {
      const startIndex = textLower.indexOf(queryLower)
      const endIndex = startIndex + query.length

      return (
        <>
          {text.substring(0, startIndex)}
          <span className="bg-yellow-200 font-semibold">
            {text.substring(startIndex, endIndex)}
          </span>
          {text.substring(endIndex)}
        </>
      )
    }

    return text
  }

  // Handle result click
  const handleResultClick = async (result: any) => {
    if (onResultClick) {
      onResultClick(result)
    } else {
      // Default behavior: Navigate to symbol page
      // Use symbol-fullname for navigation (e.g., "SZSE:399232" instead of just "399232")
      const symbolForNavigation = result['symbol-fullname'] || result.symbol
      setNavigatingSymbol(symbolForNavigation)

      try {
        // Replace colon with hyphen for URL-friendly format
        const urlFriendlySymbol = symbolForNavigation.replace(/:/g, '-')
        await router.push(`/markets/stocks/${urlFriendlySymbol}`)
      } catch (err) {
        console.error('Error navigating to symbol page:', err)
      } finally {
        setNavigatingSymbol(null)
      }
    }
    setShowResults(false)
  }

  return (
    <div
      className={`relative ${className}`}
      style={{ zIndex, position: 'relative' }}
    >
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FaSearch className="text-gray-400" />
        </div>
        <input
          ref={searchInputRef}
          type="text"
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onFocus={() => {
            if (results.length > 0) setShowResults(true)
          }}
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {loading ? (
            <FaSpinner className="text-gray-400 animate-spin" />
          ) : query ? (
            <button
              onClick={clearSearch}
              className="text-gray-400 hover:text-gray-600"
              aria-label="Clear search"
            >
              <FaTimes />
            </button>
          ) : null}
        </div>
      </div>

      {showResults && (
        <div
          ref={resultsContainerRef}
          className="absolute z-30 w-full mt-1 bg-white rounded-lg shadow-lg max-h-96 overflow-y-auto"
        >
          {loading ? (
            <div className="p-4 flex items-center justify-center text-gray-500">
              <FaSpinner className="animate-spin mr-2" />
              Searching...
            </div>
          ) : error ? (
            <div className="p-4 text-red-500">{error}</div>
          ) : results.length === 0 && hasSearched ? (
            <div className="p-4 text-gray-500">
              No results found for "{query}". Try a different search term.
            </div>
          ) : results.length === 0 ? (
            <div className="p-4 text-gray-500">Type to search for symbols</div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {results.map((result, index) => {
                const isNavigating =
                  navigatingSymbol ===
                  (result['symbol-fullname'] || result.symbol)

                // If any item is navigating, only show that item
                if (navigatingSymbol && !isNavigating) {
                  return null
                }

                return (
                  <li key={`${result.symbol}-${index}`}>
                    <button
                      className="w-full text-left px-4 py-3 hover:bg-gray-100 flex flex-col"
                      onClick={() => handleResultClick(result)}
                      disabled={isNavigating}
                    >
                      {isNavigating ? (
                        <div className="flex justify-center items-center py-1">
                          <FaSpinner className="animate-spin text-blue-500 mr-2" />
                          <span>Navigating...</span>
                        </div>
                      ) : (
                        <>
                          <div className="flex justify-between items-center">
                            <span className="font-semibold text-blue-600">
                              {highlightMatch(result.symbol)}
                            </span>
                            {result['exchange-listed'] && (
                              <span className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                                {result['exchange-listed']}
                              </span>
                            )}
                          </div>
                          {result['symbol-fullname'] && (
                            <span className="text-sm text-gray-700">
                              {highlightMatch(result['symbol-fullname'])}
                            </span>
                          )}
                          {result.description && (
                            <span className="text-xs text-gray-500 mt-1 line-clamp-2">
                              {highlightMatch(result.description)}
                            </span>
                          )}
                          {result['country'] && (
                            <div className="mt-1 flex items-center">
                              <span className="text-xs text-gray-500 mr-2">
                                {result['country']}
                              </span>
                              {result['currency-id'] && (
                                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                  {result['currency-id']}
                                </span>
                              )}
                            </div>
                          )}
                        </>
                      )}
                    </button>
                  </li>
                )
              })}
            </ul>
          )}
        </div>
      )}
    </div>
  )
}

export default SymbolSearch
