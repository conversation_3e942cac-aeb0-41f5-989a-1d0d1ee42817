import type { FC } from 'react'

export interface AttributionSectionProps {
  className?: string
}

export const AttributionSection: FC<AttributionSectionProps> = ({
  className = '',
}) => {
  return (
    <div className={`w-full mt-5 ${className}`}>
      {/* Separator Line */}
      <div className="w-full h-px bg-[#E5E5E5] mb-5" />

      {/* Attribution Content */}
      <div className="w-full px-4 sm:px-8 flex flex-col items-start">
        {/* TradingView Logo */}
        <div className="mb-4">
          <img
            src="/logos/full_logo.svg"
            alt="TradingView"
            className="w-[120px] md:w-[150px] h-auto"
            onError={(e) => {
              // Fallback if logo doesn't exist
              e.currentTarget.style.display = 'none'
            }}
          />
        </div>

        {/* Attribution and Disclaimer Text */}
        <p
          className="text-[11px] md:text-[13px] leading-[16px] md:leading-[20px] text-[#595959] font-normal max-w-full"
          style={{ fontFamily: '"Mulish", sans-serif' }}
        >
          Market Data and Widgets Technology provided by{' '}
          <a
            href="https://www.tradingview.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-[#595959] hover:text-blue-600 underline"
          >
            TradingView
          </a>
          .<br />
          Data Delayed by 10 minutes. Information is provided 'as is' and solely
          for informational purposes, not for trading purposes or advice.
        </p>
      </div>
    </div>
  )
}
