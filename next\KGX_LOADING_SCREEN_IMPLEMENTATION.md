# KGX Table Loading Screen Implementation

## Overview
The KGX table on `/markets/kitco-gold-index` (http://localhost:3000/markets/kitco-gold-index) now has a comprehensive, subtle loading screen that appears while commodity data is being loaded. The loading screen maintains the table layout and provides visual feedback without breaking the user experience.

## Features Implemented

### 🎯 **Subtle Loading Overlay**
- Semi-transparent white overlay (70% opacity) with backdrop blur
- Centered loading indicator with spinner and text
- Maintains table structure underneath for smooth transitions
- Professional styling with rounded corners and shadow

### 🔄 **Loading States**
- **Initial Load**: Shows when page first loads and data is being fetched
- **Data Refresh**: Shows during API data updates and refreshes
- **Transition States**: Smooth transitions between loading and loaded states

### 📱 **Responsive Design**
- Works on both desktop and mobile layouts
- Adapts to grouped and non-grouped table views
- Maintains accessibility and keyboard navigation

## Technical Implementation

### 1. Enhanced Loading Hook
**File:** `next/src/hooks/GlobalIndex/useGlobalIndexData.ts`

```typescript
// Now returns both data and loading state
return {
  data: createTableData(metalData, energyData, cryptoData),
  isLoading
}
```

### 2. Updated Page Component
**File:** `next/src/pages/markets/kitco-gold-index.tsx`

```typescript
// Uses destructured return from hook
const { data, isLoading } = useGlobalIndexData()
```

### 3. Loading Screen Components
**File:** `next/src/components/DataTable/KgxDataTable.tsx`

#### Loading Skeleton Component
```typescript
const LoadingSkeleton = ({ width = '100%', height = '16px', className = '' }) => (
  <div
    className={`animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] rounded-sm ${className}`}
    style={{
      width,
      height,
      animation: 'shimmer 1.5s ease-in-out infinite',
    }}
  />
)
```

#### Loading Overlay
```typescript
<div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] z-10 flex items-center justify-center transition-all duration-300">
  <div className="flex items-center space-x-3 bg-white/95 px-6 py-3 rounded-xl shadow-lg border border-gray-200/50 backdrop-blur-sm">
    <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
    <span className="text-sm text-gray-700 font-medium">
      Loading commodity data...
    </span>
  </div>
</div>
```

## Visual Design

### 🎨 **Loading Indicator**
- Blue spinning circle (matches site theme)
- "Loading commodity data..." text
- Clean, professional appearance
- Subtle animations and transitions

### 📊 **Skeleton Rows**
- Maintains table structure during loading
- Animated shimmer effect on placeholder content
- Preserves column widths and spacing
- 8 skeleton rows for non-grouped tables
- 4 skeleton rows per category for grouped tables

### 🌟 **Animations**
- Shimmer animation for skeleton elements
- Smooth fade transitions
- CSS-based animations for performance

## Browser Compatibility

### ✅ **Supported Features**
- CSS backdrop-filter for blur effects
- CSS animations and transitions
- Flexbox layouts
- CSS Grid (for table structure)

### 🔧 **Fallbacks**
- Graceful degradation if backdrop-filter not supported
- Standard opacity fallback for older browsers
- Progressive enhancement approach

## Testing

### Manual Testing
1. Navigate to `http://localhost:3000/markets/kitco-gold-index`
2. Observe loading screen on initial page load
3. Refresh page to see loading state again
4. Check both desktop and mobile views

### Browser Console Testing
Use the test script in `next/test-loading.js`:

```javascript
// Available test functions:
testKGXLoading()      // Simulate loading by hiding table
checkLoadingElements() // Check if loading elements exist
refreshKGXData()      // Refresh page to see initial loading
```

### Test Attributes
- `data-testid="kgx-table-loading"` - Non-grouped loading state
- `data-testid="kgx-table-loading-grouped"` - Grouped loading state  
- `data-testid="kgx-table-loaded"` - Loaded state

## Performance Considerations

### ⚡ **Optimizations**
- CSS-only animations (no JavaScript)
- Minimal DOM manipulation
- Efficient re-renders with React
- Backdrop blur uses GPU acceleration

### 📈 **Loading Strategy**
- Shows loading immediately when `isLoading` is true
- Maintains table structure to prevent layout shifts
- Smooth transitions prevent jarring changes

## Isolation

### 🔒 **KGX Table Only**
- All changes isolated to KGX table components
- No impact on other tables or loading states
- Uses specific test IDs for KGX table
- Maintains existing drag & drop functionality

### 🛡️ **Backward Compatibility**
- Existing functionality preserved
- No breaking changes to API
- Graceful fallbacks for older browsers

## Files Modified

1. **`next/src/hooks/GlobalIndex/useGlobalIndexData.ts`**
   - Enhanced to return loading state
   - Improved data handling logic

2. **`next/src/pages/markets/kitco-gold-index.tsx`**
   - Updated to use new hook return structure
   - Proper loading state propagation

3. **`next/src/hooks/KGX/useKGXData.ts`**
   - Updated to handle new hook structure

4. **`next/src/components/DataTable/KgxDataTable.tsx`**
   - Enhanced loading screen implementation
   - Added test attributes
   - Improved visual styling

## CSS Dependencies

The loading screen uses existing CSS animations defined in:
- `next/src/styles/global.scss` - Shimmer keyframes animation
- Tailwind CSS classes for styling and animations
