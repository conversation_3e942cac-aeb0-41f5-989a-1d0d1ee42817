'use client'

import type { FC } from 'react'
import { useEffect, useRef, useState } from 'react'

interface SymbolInfoWidgetProps {
  symbol: string
  width?: string
  locale?: string
  colorTheme?: 'light' | 'dark'
  isTransparent?: boolean
}

export const SymbolInfoWidget: FC<SymbolInfoWidgetProps> = ({
  symbol,
  width = '100%',
  locale = 'en',
  colorTheme = 'light',
  isTransparent = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient || !containerRef.current) return

    // Clear any existing content
    const container = containerRef.current
    container.innerHTML = ''

    // Create script element for TradingView symbol widget
    const script = document.createElement('script')
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-symbol-info.js'
    script.type = 'text/javascript'
    script.async = true
    script.innerHTML = JSON.stringify({
      symbol: symbol,
      width: width,
      locale: locale,
      colorTheme: colorTheme,
      isTransparent: isTransparent,
      largeChartUrl: `${process.env.NEXT_PUBLIC_URL}/markets/stocks/${symbol}`,
    })

    // Create widget container
    const widgetDiv = document.createElement('div')
    widgetDiv.className = 'tradingview-widget-container'
    widgetDiv.style.width = '100%'

    const widgetContent = document.createElement('div')
    widgetContent.className = 'tradingview-widget-container__widget'

    widgetDiv.appendChild(widgetContent)
    widgetDiv.appendChild(script)

    container.appendChild(widgetDiv)
  }, [symbol, isClient, width, locale, colorTheme, isTransparent])

  if (!isClient) {
    return (
      <div className="w-full flex items-center justify-center bg-gray-100 py-4">
        <div className="animate-pulse text-sm">Loading symbol info...</div>
      </div>
    )
  }

  return <div ref={containerRef} className="w-full" />
}
