import React, { useMemo } from 'react'

interface DateTimeProps {
  date?: string | Date | null
  timeZone?: string
  timeFormat?: string
  showTimeZone?: boolean
}

const DateTime: React.FC<DateTimeProps> = ({
  date,
  timeZone = process.env.NEXT_PUBLIC_TIMEZONE || 'America/New_York',
  timeFormat = 'MMM dd, yyyy HH:mm',
  showTimeZone = false,
}) => {
  // Use useMemo to prevent recalculation on every render
  const formattedTime = useMemo(() => {
    console.log('Input date:', date, 'Type:', typeof date)

    // Handle null/undefined dates
    if (!date) {
      console.error('Date is null or undefined:', date)
      return 'No Date'
    }

    let parsedDate: Date

    if (typeof date === 'string') {
      // If the string doesn't end with 'Z' or timezone info, treat it as UTC
      if (!date.includes('Z') && !date.includes('+') && !date.includes('T')) {
        // Add 'Z' to indicate UTC if it's a simple date string
        parsedDate = new Date(date + ' UTC')
      } else {
        parsedDate = new Date(date)
      }
    } else if (date instanceof Date) {
      parsedDate = date
    } else {
      console.error('Invalid date type:', typeof date, date)
      return 'Invalid Date Type'
    }

    // Check if parsedDate is valid before calling methods on it
    if (!parsedDate || isNaN(parsedDate.getTime())) {
      console.error('Invalid date:', date, 'Parsed as:', parsedDate)
      return 'Invalid Date'
    }

    console.log('Parsed date (UTC):', parsedDate.toISOString())

    // Check if date is valid
    if (isNaN(parsedDate.getTime())) {
      console.error('Invalid date:', date)
      return 'Invalid Date'
    }

    const options: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'America/New_York', // Convert to Eastern Time
      hour12: false,
    }

    if (showTimeZone) {
      options.timeZoneName = 'short'
    }

    const formatter = new Intl.DateTimeFormat('en-US', options)
    const parts = formatter.formatToParts(parsedDate)

    const time = parts
      .filter(
        (p) => p.type === 'hour' || p.type === 'minute' || p.type === 'literal',
      )
      .map((p) => p.value)
      .join('')
    const tz = parts.find((p) => p.type === 'timeZoneName')?.value

    const formatted = showTimeZone && tz ? `${time} (${tz})` : time
    console.log('Formatted result (Eastern Time):', formatted)

    return formatted
  }, [date, timeZone, timeFormat, showTimeZone]) // Include all props in dependency array

  console.log('DateTime', formattedTime)

  return <span>{formattedTime}</span>
}

export default DateTime
