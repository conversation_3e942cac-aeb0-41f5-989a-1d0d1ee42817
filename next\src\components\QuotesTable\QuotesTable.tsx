import currency from 'currency.js'
import Link from 'next/link'
import type { FC } from 'react'
import Table from '~/src/components/Table/Table'
import cs from '~/src/utils/cs'
import dates from '~/src/utils/dates'
import isNegative from '~/src/utils/isNegative'
import SkeletonTable from '../SkeletonTable/SkeletonTable'
import styles from './QuotesTable.module.scss'

interface Props {
  title: string
  section: 'indices' | 'stocks' | 'futures'
  data: any
  isLoading?: boolean
  showMore?: boolean
}

const QuotesTable: FC<Props> = ({
  data,
  section,
  title,
  showMore,
  isLoading,
}) => {
  const colorize = (n: number) => {
    if (isNegative(n)) {
      return cs([styles.colorRed, styles.bold])
    }
    return cs([styles.colorGreen, styles.bold])
  }

  const symbolSanitizer = (symbol: string): string => {
    const splitter = symbol.split('')
    if (splitter.includes('$') || splitter.includes('^')) {
      return symbol.substring(1)
    }
    return symbol
  }

  return (
    <Table title={title + ' — ' + dates.dayTime()}>
      <ul>
        <Titles />
        {isLoading && <Loading />}
        {data?.map((x, idx: number) => (
          <li
            className={idx % 2 ? styles.item : cs([styles.item, styles.altBg])}
            key={x.symbol}
          >
            <Link
              href={`/markets/${section}/[symbol]`}
              as={`/markets/${section}/${encodeURIComponent(
                section === 'stocks'
                  ? x['symbol-fullname'] || x.symbol
                  : x.symbol,
              )}`}
            >
              <>
                <div className="hidden md:block lg:block">
                  <p className="font-semibold">{symbolSanitizer(x.symbol)}</p>
                </div>
                <p className="mt-1 text-xs">{x?.name || x?.symbolName}</p>
                <p className="text-right font-semibold">
                  {currency(x?.lastPrice).format()}
                </p>
                <div className="hidden md:block lg:block">
                  <p
                    className={cs([
                      colorize(x?.netChange || x?.priceNetChange),
                      'text-right',
                    ])}
                  >
                    {x?.netChange
                      ? currency(x?.netChange).format()
                      : currency(x?.priceNetChange).format()}
                    &nbsp;
                    {'('}
                    {x?.percentChange?.toFixed(2) ||
                      x?.pricePercentChange?.toFixed(2)}
                    &#37;{')'}
                  </p>
                </div>
              </>
            </Link>
          </li>
        ))}
        {showMore && (
          <li className={styles.itemMoreLink}>
            <div className="flex-shrink-1 mx-auto flex max-w-xs justify-center">
              <Link href={`/markets/${section}`} className="moreLink">
                More&nbsp;{section}&nbsp;+
              </Link>
            </div>
          </li>
        )}
      </ul>
    </Table>
  )
}

export default QuotesTable

const Titles = () => (
  <li className={cs([styles.item, styles.titles])}>
    <p className="hidden md:block lg:block">Symbol</p>
    <p>Name</p>
    <p className="text-right">Last</p>
    <p className="hidden text-right md:block lg:block">Change</p>
  </li>
)

const Loading = () => (
  <>
    {[1, 2, 3, 4, 5].map((x) => (
      <li key={x} className={cs([styles.item, styles.loading])}>
        <SkeletonTable />
        <div className="hidden md:flex">
          <SkeletonTable />
        </div>
        <div className="hidden justify-end md:flex">
          <SkeletonTable />
        </div>
        <div className="flex justify-end">
          <SkeletonTable />
        </div>
      </li>
    ))}
  </>
)
