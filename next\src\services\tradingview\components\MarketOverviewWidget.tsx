'use client'

import { useEffect, useRef } from 'react'

interface MarketOverviewWidgetProps {
  width?: string
  height?: string
  colorTheme?: 'light' | 'dark'
  dateRange?: string
  showChart?: boolean
  locale?: string
  isTransparent?: boolean
  showSymbolLogo?: boolean
  showFloatingTooltip?: boolean
}

export default function MarketOverviewWidget({
  width = '100%',
  height = '500',
  colorTheme = 'light',
  dateRange = '12M',
  showChart = true,
  locale = 'en',
  isTransparent = false,
  showSymbolLogo = true,
  showFloatingTooltip = false,
}: MarketOverviewWidgetProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    const script = document.createElement('script')
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-market-overview.js'
    script.async = true
    script.innerHTML = JSON.stringify({
      colorTheme,
      dateRange,
      showChart,
      locale,
      largeChartUrl: `${process.env.NEXT_PUBLIC_URL}/markets/stocks/`,
      isTransparent,
      showSymbolLogo,
      showFloatingTooltip,
      width,
      height,
      plotLineColorGrowing: 'rgba(41, 98, 255, 1)',
      plotLineColorFalling: 'rgba(41, 98, 255, 1)',
      gridLineColor: 'rgba(46, 46, 46, 0)',
      scaleFontColor: 'rgba(15, 15, 15, 1)',
      belowLineFillColorGrowing: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorFalling: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorGrowingBottom: 'rgba(41, 98, 255, 0)',
      belowLineFillColorFallingBottom: 'rgba(41, 98, 255, 0)',
      symbolActiveColor: 'rgba(41, 98, 255, 0.12)',
      tabs: [
        {
          title: 'Indices',
          symbols: [
            { s: 'FOREXCOM:SPXUSD', d: 'S&P 500 Index' },
            { s: 'FOREXCOM:NSXUSD', d: 'US 100 Cash CFD' },
            { s: 'FOREXCOM:DJI', d: 'Dow Jones Industrial Average Index' },
            { s: 'INDEX:NKY', d: 'Japan 225' },
            { s: 'INDEX:DEU40', d: 'DAX Index' },
            { s: 'FOREXCOM:UKXGBP', d: 'FTSE 100 Index' },
          ],
          originalTitle: 'Indices',
        },
        /* Removed Forex for now
        {
          title: 'Forex',
          symbols: [
            { s: 'FX:EURUSD', d: 'EUR to USD' },
            { s: 'FX:GBPUSD', d: 'GBP to USD' },
            { s: 'FX:USDJPY', d: 'USD to JPY' },
            { s: 'FX:USDCHF', d: 'USD to CHF' },
            { s: 'FX:AUDUSD', d: 'AUD to USD' },
            { s: 'FX:USDCAD', d: 'USD to CAD' },
          ],
          originalTitle: 'Forex',
        },
        */
        {
          title: 'Futures',
          symbols: [
            { s: 'BMFBOVESPA:ISP1!', d: 'S&P 500 Index Futures' },
            { s: 'BMFBOVESPA:EUR1!', d: 'Euro Futures' },
            { s: 'PYTH:WTI3!', d: 'WTI CRUDE OIL' },
            { s: 'BMFBOVESPA:ETH1!', d: 'Hydrous ethanol' },
            { s: 'BMFBOVESPA:CCM1!', d: 'Corn' },
          ],
          originalTitle: 'Futures',
        },
        {
          title: 'Bonds',
          symbols: [
            { s: 'EUREX:FGBL1!', d: 'Euro Bund' },
            { s: 'EUREX:FBTP1!', d: 'Euro BTP' },
            { s: 'EUREX:FGBM1!', d: 'Euro BOBL' },
          ],
          originalTitle: 'Bonds',
        },
      ],
    })

    containerRef.current.appendChild(script)

    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
    }
  }, [
    width,
    height,
    colorTheme,
    dateRange,
    showChart,
    locale,
    isTransparent,
    showSymbolLogo,
    showFloatingTooltip,
  ])

  return (
    <div className="tradingview-widget-container">
      <div
        className="tradingview-widget-container__widget"
        ref={containerRef}
      ></div>
      <div className="tradingview-widget-copyright">
        <a
          href="https://www.tradingview.com/"
          rel="noopener nofollow"
          target="_blank"
        >
          <span className="blue-text">Track all markets on TradingView</span>
        </a>
      </div>
    </div>
  )
}
