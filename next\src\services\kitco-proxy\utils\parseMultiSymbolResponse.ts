import type { KitcoValue } from '../types/KitcoValue'

/**
 * Parses the multi-symbol compact response format from Kitco proxy
 * Format: XAU2025-07-30 14:00:00209.20\-1.93\-0.91 HUI2025-07-30 14:00:00431.83\-4.79\-1.10 SPTTGD2025-07-30 14:00:00513.36\-4.54\-0.88
 *
 * @param responseText - Raw response text from the API
 * @returns Array of parsed KitcoValue objects
 */
export function parseMultiSymbolResponse(responseText: string): KitcoValue[] {
  try {
    const cleanText = responseText.trim()

    if (!cleanText) {
      throw new Error('Empty response text')
    }

    const results: KitcoValue[] = []

    // Split by spaces to get individual symbol data
    const symbolBlocks = cleanText.split(' ')

    for (const block of symbolBlocks) {
      if (!block.trim()) continue

      const parsed = parseSymbolBlock(block)
      if (parsed) {
        results.push(parsed)
      }
    }

    return results
  } catch (error) {
    console.error('Error parsing multi-symbol response:', error)
    console.error('Raw response:', responseText)
    return []
  }
}

/**
 * Parses a single symbol block from the response
 * Format: SYMBOL2025-07-30 14:00:00209.20\-1.93\-0.91
 *
 * @param block - Single symbol data block
 * @returns Parsed KitcoValue object or null if parsing fails
 */
function parseSymbolBlock(block: string): KitcoValue | null {
  try {
    // Extract symbol by finding where the timestamp starts (first digit after letters)
    const timestampStartMatch = block.match(/[A-Z]+(\d{4}-\d{2}-\d{2})/)
    if (!timestampStartMatch) {
      throw new Error('Could not find timestamp start in block')
    }

    const symbolLength = block.indexOf(timestampStartMatch[1]) - 4 // Subtract 4 for the year
    const symbol = block.substring(0, symbolLength)
    const dataText = block.substring(symbolLength)

    // Split by backslash to get the parts
    const parts = dataText.split('\\')

    if (parts.length < 3) {
      throw new Error('Invalid block format - expected at least 3 parts')
    }

    // Extract timestamp and price from the first part
    // Format: 2025-07-30 14:00:00209.20
    const timestampAndPrice = parts[0]

    // Extract timestamp (first 19 characters: YYYY-MM-DD HH:MM:SS)
    const timestamp = timestampAndPrice.substring(0, 19)

    // Extract price (remaining characters)
    const priceText = timestampAndPrice.substring(19)
    const price = parseFloat(priceText)

    // Extract change and changePercentage
    const change = parseFloat(parts[1])
    const changePercentage = parseFloat(parts[2])

    // Validate parsed values
    if (!symbol || isNaN(price) || isNaN(change) || isNaN(changePercentage)) {
      throw new Error('Failed to parse values from block')
    }

    if (!timestamp || timestamp.length !== 19) {
      throw new Error('Invalid timestamp format')
    }

    return {
      symbol,
      timestamp,
      price,
      change,
      changePercentage,
    }
  } catch (error) {
    console.error('Error parsing symbol block:', error)
    console.error('Block:', block)
    return null
  }
}

/**
 * Validates that the response text looks like a valid multi-symbol response
 *
 * @param responseText - Raw response text to validate
 * @returns True if the response looks valid
 */
export function isValidMultiSymbolResponse(responseText: string): boolean {
  return (
    responseText.trim().length > 0 &&
    responseText.includes('\\') &&
    (responseText.includes('XAU') ||
      responseText.includes('HUI') ||
      responseText.includes('SPTTGD'))
  )
}
