import clsx from 'clsx'
import dynamic from 'next/dynamic'
import Link from 'next/link'
import { useEffect } from 'react'
import { mapBarchartToTradingView } from '~/src/services/tradingview/utils/symbolMapping'

// Import TradingView widget dynamically for client-side rendering only
const TradingViewWidget = dynamic(
  () => import('~/src/services/tradingview/components/TradingViewWidget'),
  { ssr: false },
)

interface TradingViewChartProps {
  symbol: string
  title?: string
  href?: string
  className?: string
}

/**
 * TradingView chart component that replaces the Barcharts component
 * Maintains the same interface for easy replacement
 */
export function TradingViewChart({
  symbol,
  title,
  href,
  className,
}: TradingViewChartProps) {
  // Convert Barchart symbol to TradingView symbol
  const tradingViewSymbol = mapBarchartToTradingView(symbol)

  const handleError = (error: string) => {
    console.error(`TradingView chart error for symbol ${symbol}:`, error)
  }

  useEffect(() => {
    console.log('TradingView chart symbol:', tradingViewSymbol)
  }, [tradingViewSymbol])

  return (
    <div>
      {href && title && (
        <Link
          href={href}
          className={clsx(
            'block',
            'text-base font-semibold text-black',
            'hover:underline',
            'mb-2',
          )}
        >
          {title}
        </Link>
      )}
      <div
        className={clsx(
          className,
          'h-[280px] w-full rounded-md overflow-hidden bg-white border border-gray-200',
        )}
      >
        <TradingViewWidget
          symbol={tradingViewSymbol}
          onError={handleError}
          theme="light"
          autosize={true}
          height={280}
          interval="D"
          timezone="Etc/UTC"
          style="1"
          locale="en"
          toolbar_bg="#f1f3f6"
          enable_publishing={false}
          withdateranges={false}
          hide_side_toolbar={true}
          allow_symbol_change={false}
          save_image={false}
          show_popup_button={false}
        />
      </div>
    </div>
  )
}
