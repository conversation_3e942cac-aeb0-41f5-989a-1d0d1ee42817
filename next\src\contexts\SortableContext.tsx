import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import type { ReactNode } from 'react'
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react'

interface SortableContextType<T = string> {
  items: T[]
  moveRow: (dragIndex: number, hoverIndex: number) => void
  isDragging: boolean
}

export const SortableTableContext = createContext<SortableContextType<any>>({
  items: [],
  moveRow: () => {},
  isDragging: false,
})

interface SortableProviderProps<T = string> {
  children: ReactNode
  initialItems: T[]
  onDragEnd?: (event: DragEndEvent) => void
  getItemId?: (item: T) => string
  storageKey?: string
}

export const SortableProvider = <T = string,>({
  children,
  initialItems,
  onDragEnd,
  getItemId = (item: T) => String(item),
  storageKey = 'commodityOrder',
}: SortableProviderProps<T>) => {
  const [items, setItems] = useState<T[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Set client flag on mount
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Load saved order and handle data updates
  useEffect(() => {
    if (!isClient || !initialItems.length) return

    try {
      const savedOrder = localStorage.getItem(storageKey)
      if (savedOrder) {
        const parsed = JSON.parse(savedOrder)
        // console.log(`[SortableContext] Loading saved order for ${storageKey}:`, parsed)
        // For commodity objects, we need to match by ID and preserve the full object
        if (Array.isArray(parsed)) {
          if (typeof initialItems[0] === 'object' && initialItems[0] !== null) {
            // Reorder initialItems based on saved order, but include all items from initialItems
            const reorderedItems: T[] = []
            const usedItems = new Set<string>()

            // First, add items in the saved order
            parsed.forEach((savedId: string) => {
              const item = initialItems.find(
                (item) => getItemId(item) === savedId,
              )
              if (item) {
                reorderedItems.push(item)
                usedItems.add(getItemId(item))
              }
            })

            // Then, add any new items that weren't in the saved order
            initialItems.forEach((item) => {
              const itemId = getItemId(item)
              if (!usedItems.has(itemId)) {
                reorderedItems.push(item)
              }
            })

            // console.log(`[SortableContext] Applied saved order, ${reorderedItems.length} items reordered`)
            setItems(reorderedItems)
          } else {
            // For simple string arrays
            setItems(parsed as T[])
          }
        } else {
          setItems(initialItems)
        }
      } else {
        // Initialize with default order if no saved order exists
        // console.log(`[SortableContext] No saved order found for ${storageKey}, using default order`)
        setItems(initialItems)
      }
    } catch (e) {
      console.error('Failed to load saved order', e)
      setItems(initialItems)
    }
  }, [initialItems, getItemId, storageKey, isClient])

  const moveRow = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      if (dragIndex === hoverIndex) return

      setItems((prevItems) => {
        const newItems = [...prevItems]
        const [removed] = newItems.splice(dragIndex, 1)
        newItems.splice(hoverIndex, 0, removed)

        // Save to localStorage
        if (typeof window !== 'undefined') {
          try {
            // Save IDs for objects, or the items themselves for primitives
            const itemsToSave =
              typeof newItems[0] === 'object' && newItems[0] !== null
                ? newItems.map((item) => getItemId(item))
                : newItems
            localStorage.setItem(storageKey, JSON.stringify(itemsToSave))
            // console.log(`[SortableContext] Saved new order for ${storageKey}:`, itemsToSave)
          } catch (e) {
            console.error('Failed to save order', e)
          }
        }

        return newItems
      })
    },
    [getItemId, storageKey],
  )

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleDragStart = useCallback(() => {
    setIsDragging(true)
  }, [])

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event
      // console.log(`[SortableContext] Drag end event:`, { activeId: active?.id, overId: over?.id })

      if (active && over && active.id !== over.id) {
        const oldIndex = items.findIndex(
          (item) => getItemId(item) === active.id,
        )
        const newIndex = items.findIndex((item) => getItemId(item) === over.id)

        // console.log(`[SortableContext] Moving item from index ${oldIndex} to ${newIndex}`)

        if (oldIndex !== -1 && newIndex !== -1) {
          moveRow(oldIndex, newIndex)
        } else {
          // console.warn(`[SortableContext] Could not find indices for drag operation`, { oldIndex, newIndex, activeId: active.id, overId: over.id })
        }
      }

      // Call the parent's onDragEnd if provided
      if (onDragEnd) {
        onDragEnd(event)
      }

      setIsDragging(false)
    },
    [items, moveRow, onDragEnd, getItemId],
  )

  // Convert items to IDs for SortableContext
  const itemIds = items.map((item) => getItemId(item))

  return (
    <SortableTableContext.Provider value={{ items, moveRow, isDragging }}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={itemIds} strategy={verticalListSortingStrategy}>
          {children}
        </SortableContext>
      </DndContext>
    </SortableTableContext.Provider>
  )
}

// Hook to use the sortable context
export const useSortableTable = <T = any,>() => {
  const context = useContext(SortableTableContext)
  if (!context) {
    throw new Error('useSortableTable must be used within a SortableProvider')
  }
  return context as SortableContextType<T>
}
