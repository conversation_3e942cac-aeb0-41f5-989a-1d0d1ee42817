import { type FC } from 'react'
import OtherIndicators from '~/src/components/OtherIndicators/OtherIndicators'
import { useKitcoOtherIndices } from '~/src/hooks/kitco-proxy/useKitcoOtherIndices'

const OtherIndicesCell: FC = () => {
  const { data: kitcoData } = useKitcoOtherIndices()

  // Symbol mapping for display names
  const symbolMapping = {
    DJI: 'Dow Jones',
    IXIC: 'NASDAQ',
    SPX: 'S&P 500',
    NYA: 'NYSE',
    N225: 'Nikkei',
    GSPTSE: 'TSX',
  } as const

  // Transform Kitco data to match expected structure
  const transformedData = {
    GetBarchartQuotes: {
      __typename: 'BarchartGetQuote' as const,
      timestamp: Date.now() / 1000,
      symbols: kitcoData?.indices.map((item) => item.symbol).join(',') || '',
      results:
        kitcoData?.indices.map((item) => ({
          __typename: 'BarchartQuote' as const,
          symbol: item.symbol,
          name:
            symbolMapping[item.symbol as keyof typeof symbolMapping] ||
            item.symbol,
          lastPrice: item.price,
          percentChange: item.changePercentage,
          netChange: item.change,
          high: item.price, // Using current price as placeholder
          low: item.price, // Using current price as placeholder
          open: item.price, // Using current price as placeholder
          volume: 0, // No volume data available
          serverTimestamp: item.timestamp,
        })) || [],
    },
  }

  return (
    <div>
      <OtherIndicators title="Market Indices" data={transformedData} />
    </div>
  )
}
export default OtherIndicesCell
