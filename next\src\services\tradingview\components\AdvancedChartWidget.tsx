'use client'

import type { FC } from 'react'
import { memo, useEffect, useRef, useState } from 'react'

interface AdvancedChartWidgetProps {
  symbol: string
  height?: string | number
  mobileHeight?: string | number
}

const AdvancedChartWidget: FC<AdvancedChartWidgetProps> = ({
  symbol,
  height = '468',
  mobileHeight,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isMobile, setIsMobile] = useState(false)

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Check on mount
    checkMobile()

    // Add resize listener
    window.addEventListener('resize', checkMobile)

    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  }, [])

  // Determine which height to use
  const currentHeight =
    isMobile && mobileHeight !== undefined ? mobileHeight : height

  useEffect(() => {
    if (!containerRef.current) return

    // Clear any existing content
    containerRef.current.innerHTML = ''

    const script = document.createElement('script')
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js'
    script.type = 'text/javascript'
    script.async = true
    script.innerHTML = JSON.stringify({
      autosize: true,
      symbol: symbol,
      interval: 'D',
      timezone: 'America/New_York',
      theme: 'light',
      style: '1',
      height:
        typeof currentHeight === 'number'
          ? currentHeight.toString()
          : currentHeight,
      locale: 'en',
      withdateranges: true,
      hide_side_toolbar: false,
      allow_symbol_change: false,
      support_host: 'https://www.tradingview.com',
      largeChartUrl: `${process.env.NEXT_PUBLIC_URL}/markets/stocks/${symbol}`,
    })

    containerRef.current.appendChild(script)
  }, [symbol, currentHeight])

  return (
    <div
      className="tradingview-widget-container"
      ref={containerRef}
      style={{
        height:
          typeof currentHeight === 'number'
            ? `${currentHeight}px`
            : currentHeight,
        width: '100%',
      }}
    />
  )
}

export default memo(AdvancedChartWidget)
