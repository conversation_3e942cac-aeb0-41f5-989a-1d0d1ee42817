import { elasticClient } from '../client/elasticsearch'
import { TradingViewSymbol } from '../types/TradingViewSymbol'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Get all symbols from a specific exchange.
 */
export async function filterByExchange(
  exchange: string,
): Promise<TradingViewSymbol[]> {
  try {
    const response = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1000,
      query: {
        term: { 'exchange-listed': exchange.toLowerCase() },
      },
    })

    return response.hits.hits.map((hit) => hit._source as TradingViewSymbol)
  } catch (error) {
    console.error(
      'Error filtering symbols by exchange in Elasticsearch:',
      error,
    )
    return []
  }
}
