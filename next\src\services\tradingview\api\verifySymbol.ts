/**
 * Verify if a symbol exists via API
 * @param symbol Symbol to verify
 * @returns Promise resolving to boolean - true if symbol exists
 */
export async function verifySymbol(symbol: string): Promise<boolean> {
  try {
    const response = await fetch(
      `/api/symbol/verify?symbol=${encodeURIComponent(symbol)}`,
    )

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.exists
  } catch (error) {
    console.error('Symbol verification API error:', error)
    return false
  }
}
