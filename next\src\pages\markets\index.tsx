import { AdvertisingSlot } from 'react-advertising'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import Layout from '~/src/components/Layout/Layout'
import PageLayoutTwoColumns from '~/src/components/PageLayoutTwoColumns/PageLayoutTwoColumns'
import SymbolSearch from '~/src/components/SymbolSearch/SymbolSearch'
import HotlistsWidget from '~/src/services/tradingview/components/HotlistsWidget'
import MarketOverviewWidget from '~/src/services/tradingview/components/MarketOverviewWidget'
import MarketQuotesWidget from '~/src/services/tradingview/components/MarketQuotesWidget'
import styles from './index.module.scss'

const MarketsPage = () => {
  return (
    <Layout title={'Stock Markets | DJIA, DOW, NASDAQ, S&P 500, NYSE'}>
      <PageLayoutTwoColumns>
        <main>
          <h1 className="mb-8 text-4xl font-bold">All Markets</h1>

          <div className="mb-8">
            <div className="bg-white p-5 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-3">Find Stocks</h2>
              <p className="text-gray-600 mb-3">
                Search for stocks by symbol or company name
              </p>
              <SymbolSearch placeholder="Search for a stock (e.g., AAPL, Microsoft)" />
            </div>
          </div>

          <section className={styles.block}>
            <div className="grid grid-cols-1 gap-6">
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h2 className="text-xl font-semibold mb-4">Market Overview</h2>
                <MarketOverviewWidget width="100%" height="500" />
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h2 className="text-xl font-semibold mb-4">Market Hotlists</h2>
                <HotlistsWidget width="100%" height="500" />
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h2 className="text-xl font-semibold mb-4">Market Quotes</h2>
                <MarketQuotesWidget width="100%" height={500} />
              </div>
            </div>
          </section>

          <section className={styles.block}>
            <AdvertisingSlot
              id={'banner-2'}
              className="mx-auto h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] desktop:h-[90px] desktop:w-[728px] no-print"
            />
          </section>
        </main>
        <aside>
          <div className={styles.block}>
            <AdvertisingSlot
              id={'right-rail-1'}
              className="mx-auto hidden h-[250px] w-[300px] desktop:flex no-print"
            />
          </div>
          <div className={styles.block}>
            <LatestNewsCell />
          </div>
          <div className={styles.block}>
            <AdvertisingSlot
              id={'right-rail-2'}
              className="mx-auto hidden h-[600px] w-[300px] desktop:flex no-print"
            />
          </div>
        </aside>
      </PageLayoutTwoColumns>
    </Layout>
  )
}

export default MarketsPage
