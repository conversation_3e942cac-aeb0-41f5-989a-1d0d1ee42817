import { elasticClient } from '../client/elasticsearch'
import { TradingViewSymbol } from '../types/TradingViewSymbol'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Fetch all symbols from Elasticsearch using scroll API for large datasets
 * @param limit Number of symbols to fetch per batch
 * @param scrollId Optional scroll ID for continuing pagination
 * @returns Promise resolving to symbols data, total count, and scroll ID
 */
export async function getAllSymbolsFromElastic(
  limit: number = 1000,
  scrollId?: string,
): Promise<{ symbols: TradingViewSymbol[]; total: number; scrollId?: string }> {
  try {
    let response: any

    if (scrollId) {
      // Continue scrolling with existing scroll ID
      console.log(`Continuing scroll with ID: ${scrollId.substring(0, 20)}...`)

      response = await elasticClient.scroll({
        scroll_id: scrollId,
        scroll: '5m', // Keep scroll context alive for 5 minutes
      })
    } else {
      // Initial search with scroll
      console.log(`Starting new scroll search - Limit: ${limit}`)

      response = await elasticClient.search({
        index: SYMBOLS_INDEX,
        size: limit,
        scroll: '5m', // Keep scroll context alive for 5 minutes
        _source: [
          'symbol',
          'symbol-fullname',
          'description',
          'exchange-listed',
        ],
        query: {
          match_all: {},
        },
        sort: [{ 'symbol.keyword': { order: 'asc' } }],
      })
    }

    const symbols = response.hits.hits.map(
      (hit) => hit._source as TradingViewSymbol,
    )

    const total = symbols.length
    const newScrollId = response._scroll_id

    console.log(`Fetched ${symbols.length} symbols via scroll API`)

    return {
      symbols,
      total,
      scrollId: newScrollId,
    }
  } catch (error) {
    console.error('Error fetching symbols from Elasticsearch:', error)
    return {
      symbols: [],
      total: 0,
    }
  }
}

/**
 * Get total count of symbols in Elasticsearch
 * @returns Promise resolving to total symbol count
 */
export async function getSymbolsCount(): Promise<number> {
  try {
    const response = await elasticClient.count({
      index: SYMBOLS_INDEX,
      query: {
        match_all: {},
      },
    })

    return response.count
  } catch (error) {
    console.error('Error getting symbols count from Elasticsearch:', error)
    return 0
  }
}

/**
 * Clear scroll context to free up resources
 * @param scrollId Scroll ID to clear
 */
export async function clearScroll(scrollId: string): Promise<void> {
  try {
    await elasticClient.clearScroll({
      scroll_id: scrollId,
    })
    console.log('Scroll context cleared successfully')
  } catch (error) {
    console.error('Error clearing scroll context:', error)
  }
}
