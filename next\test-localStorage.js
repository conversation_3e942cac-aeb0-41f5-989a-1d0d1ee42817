// Test script to verify localStorage functionality for KGX table
// Run this in the browser console on http://localhost:3000/markets/kitco-gold-index

console.log('=== KGX Table localStorage Test ===');

// Check if localStorage is available
if (typeof Storage !== "undefined") {
  console.log('✓ localStorage is available');

  // Check current stored order
  const currentOrder = localStorage.getItem('goldIndexOrder');
  console.log('Current stored order:', currentOrder ? JSON.parse(currentOrder) : 'None');

  // Instructions for manual testing
  console.log('\n=== Manual Testing Instructions ===');
  console.log('1. Drag and drop rows in the KGX table');
  console.log('2. Check browser console for drag events');
  console.log('3. Refresh the page');
  console.log('4. Verify the order is preserved');
  console.log('5. Try dragging again to test saving');

} else {
  console.log('✗ localStorage is not available');
}

// Function to clear the stored order
window.clearKGXOrder = function () {
  localStorage.removeItem('goldIndexOrder');
  console.log('✓ Cleared KGX order from localStorage');
  location.reload();
};

// Function to show current order
window.showKGXOrder = function () {
  const order = localStorage.getItem('goldIndexOrder');
  console.log('Current KGX order:', order ? JSON.parse(order) : 'None');
  return order ? JSON.parse(order) : null;
};

console.log('\nAvailable functions:');
console.log('- clearKGXOrder() - Clear saved order and reload');
console.log('- showKGXOrder() - Show current saved order');
