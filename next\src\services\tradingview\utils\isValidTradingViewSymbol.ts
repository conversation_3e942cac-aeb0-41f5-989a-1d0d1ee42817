import { verifySymbol } from '../api/verifySymbol'
import { symbolExistsInElastic } from './symbolExistsInElastic'

/**
 * Validate if a symbol exists in TradingView
 * @param symbol - The symbol to validate
 * @returns Promise that resolves to true if symbol is valid, false otherwise
 */
export async function isValidTradingViewSymbol(
  symbol: string,
): Promise<boolean> {
  try {
    // Basic validation - symbols should only contain letters, numbers, dots, hyphens, colons, underscores, and slashes
    const isValidFormat = /^[A-Za-z0-9\._\-:\/]+$/.test(symbol)
    if (!isValidFormat) {
      return false
    }

    // Check if the symbol exists via our API
    const exists = await verifySymbol(symbol)

    // If it exists via API, return true
    if (exists) {
      return true
    }

    // Try checking directly in Elasticsearch as a backup
    const existsInElastic = await symbolExistsInElastic(symbol)
    return existsInElastic
  } catch (error) {
    console.error('Error validating TradingView symbol:', error)
    // Return false if there's any error with validation
    return false
  }
}
