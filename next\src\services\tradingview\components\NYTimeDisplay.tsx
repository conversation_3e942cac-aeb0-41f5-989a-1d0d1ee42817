'use client'

import type { FC } from 'react'
import { useEffect, useState } from 'react'

export const NYTimeDisplay: FC = () => {
  const [currentTime, setCurrentTime] = useState<string>('')

  useEffect(() => {
    const updateTime = () => {
      const now = new Date()
      const nyTime = new Intl.DateTimeFormat('en-US', {
        timeZone: 'America/New_York',
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      }).format(now)

      setCurrentTime(nyTime)
    }

    updateTime()
    const interval = setInterval(updateTime, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [])

  if (!currentTime) return null

  return <div className="text-sm text-ktc-date-gray">{currentTime} NY Time</div>
}
