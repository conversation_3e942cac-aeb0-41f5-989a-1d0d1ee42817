import Link from 'next/link'
import type { FC } from 'react'
import SymbolSearch from '~/src/components/SymbolSearch/SymbolSearch'
import cs from '~/src/utils/cs'

interface StocksPageTitleProps {
  symbol: string
  symbolData?: any
}

export const StocksPageTitle: FC<StocksPageTitleProps> = ({
  symbol,
  symbolData,
}) => {
  const displaySymbol = symbolData?.['symbol'] || symbol

  const baseH1Classes = cs([
    'font-babasNeue',
    'uppercase',
    'md:text-[48px]',
    'text-[32px]',
  ])

  const baseSubH1Classes = cs([
    'font-babasNeue',
    'uppercase',
    'md:text-[48px]',
    'md:leading-[58px]',
    'text-[32px]',
    'leading-[38px]',
  ])

  const parentTextColor = 'text-ktc-date-gray'
  const dividerSpacing = 'px-1 md:px-2'

  return (
    <div className="mb-5 flex justify-between border-b border-ktc-borders pb-4 flex-col md:flex-row">
      <div className="mb-2 block items-center justify-between gap-5 md:flex">
        <div>
          <div className="flex flex-wrap items-center leading-[38px] md:leading-[58px]">
            <Link href="/markets">
              <h1 className={cs([baseH1Classes, parentTextColor])}>Markets</h1>
            </Link>
            <h1
              className={cs([baseH1Classes, parentTextColor, dividerSpacing])}
            >
              /
            </h1>
            <Link href="/markets">
              <h1 className={cs([baseH1Classes, parentTextColor])}>Stocks</h1>
            </Link>
            <h1
              className={cs([baseH1Classes, parentTextColor, dividerSpacing])}
            >
              /
            </h1>
            <h1 className={cs([baseSubH1Classes, 'text-kitco-black'])}>
              {displaySymbol}
            </h1>
          </div>
        </div>
      </div>

      <div className="flex items-center">
        <div className="w-[400px]">
          <SymbolSearch placeholder="Search for a stock (e.g., AAPL, Microsoft)" />
        </div>
      </div>
    </div>
  )
}
