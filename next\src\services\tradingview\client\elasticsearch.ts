import { Client } from '@elastic/elasticsearch'

// Use only the environment variable for Elasticsearch URL
const getElasticUrl = (): string | null => {
  if (!process.env.ELASTIC_TRADINGVIEW_URL) {
    console.warn(
      'ELASTIC_TRADINGVIEW_URL environment variable is not set - Elasticsearch features will be disabled',
    )
    return null
  }
  return process.env.ELASTIC_TRADINGVIEW_URL
}

const elasticUrl = getElasticUrl()

// Create client only if URL is available
export const elasticClient = elasticUrl
  ? new Client({
      node: elasticUrl,
      requestTimeout: 10000, // 10 seconds
      maxRetries: 3,
      tls: {
        rejectUnauthorized: false, // For development only - allows self-signed certs
      },
    })
  : null

// Helper function to check if Elasticsearch is available
export const isElasticAvailable = (): boolean => {
  return elasticClient !== null
}
