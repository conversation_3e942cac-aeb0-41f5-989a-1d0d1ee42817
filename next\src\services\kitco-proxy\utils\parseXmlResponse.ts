import type { KitcoApiResponse } from '../types/KitcoApiResponse'
import type { KitcoValue } from '../types/KitcoValue'

/**
 * Parses the XML response from Kitco proxy and converts it to typed objects
 *
 * @param xmlString - Raw XML string from the API
 * @returns Parsed and typed response
 */
export async function parseXmlResponse(
  xmlString: string,
): Promise<KitcoApiResponse> {
  try {
    // Parse XML using DOMParser (browser) or xml2js (server-side)
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml')

    // Check for parsing errors
    const parserError = xmlDoc.querySelector('parsererror')
    if (parserError) {
      throw new Error(`XML parsing error: ${parserError.textContent}`)
    }

    // Extract all Value elements
    const valueElements = xmlDoc.querySelectorAll('Value')
    const values: KitcoValue[] = []

    valueElements.forEach((valueElement) => {
      const value = parseValueElement(valueElement)
      if (value) {
        values.push(value)
      }
    })

    return { values }
  } catch (error) {
    console.error('Error parsing Kitco XML response:', error)
    throw new Error(
      `Failed to parse XML response: ${error instanceof Error ? error.message : 'Unknown error'}`,
    )
  }
}

/**
 * Parses a single Value XML element into a KitcoValue object
 *
 * @param valueElement - XML Element representing a single Value
 * @returns Parsed KitcoValue object or null if parsing fails
 */
function parseValueElement(valueElement: Element): KitcoValue | null {
  try {
    const getElementText = (tagName: string): string | null => {
      const element = valueElement.querySelector(tagName)
      return element?.textContent?.trim() || null
    }

    const getElementNumber = (tagName: string): number | undefined => {
      const text = getElementText(tagName)
      return text ? parseFloat(text) : undefined
    }

    const symbol = getElementText('Symbol')
    const timestamp = getElementText('Timestamp')
    const price = getElementNumber('Price')
    const change = getElementNumber('Change')
    const changePercentage = getElementNumber('ChangePercentage')

    // Validate required fields
    if (
      !symbol ||
      !timestamp ||
      price === undefined ||
      change === undefined ||
      changePercentage === undefined
    ) {
      console.warn('Missing required fields in Value element:', {
        symbol,
        timestamp,
        price,
        change,
        changePercentage,
      })
      return null
    }

    // Parse optional fields
    const changeUSD = getElementNumber('ChangeUSD')
    const changePercentUSD = getElementNumber('ChangePercentUSD')
    const changeTrade = getElementNumber('ChangeTrade')
    const changePercentTrade = getElementNumber('ChangePercentTrade')

    return {
      symbol,
      timestamp,
      price,
      change,
      changePercentage,
      ...(changeUSD !== undefined && { changeUSD }),
      ...(changePercentUSD !== undefined && { changePercentUSD }),
      ...(changeTrade !== undefined && { changeTrade }),
      ...(changePercentTrade !== undefined && { changePercentTrade }),
    }
  } catch (error) {
    console.error('Error parsing Value element:', error)
    return null
  }
}
