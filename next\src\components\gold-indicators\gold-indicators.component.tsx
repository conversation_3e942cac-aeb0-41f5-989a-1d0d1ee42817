import clsx from 'clsx'
import Link from 'next/link'
import type { FC } from 'react'
import { HiArrowSmDown, HiArrowSmUp } from 'react-icons/hi'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import { useKitcoGoldIndicators } from '~/src/hooks/kitco-proxy/useKitcoGoldIndicators'
import colorize from '~/src/utils/colorize'
import cs from '~/src/utils/cs'
import isNegative from '~/src/utils/isNegative'
import styles from './gold-indicators.module.scss'

export const GoldIndicators: FC = () => {
  const { data: kitcoData } = useKitcoGoldIndicators()

  const arrowUpOrDown = (v: number) => {
    if (!isNegative(v)) {
      return 'up'
    }
    return 'down'
  }

  // Symbol mapping for display names and URLs
  const symbolMapping = {
    XAU: { name: 'XAU', url: '$XAU' },
    HUI: { name: '<PERSON><PERSON>', url: '$HUI' },
    SPTTGD: { name: 'TSX', url: '$TTGD' },
  } as const

  // Use the live data if available, otherwise show empty
  const indicators = kitcoData?.indicators || []

  return (
    <BlockShell title="Gold Indicators">
      {indicators.map((indicator, idx: number) => {
        const symbolInfo =
          symbolMapping[indicator.symbol as keyof typeof symbolMapping]

        if (!symbolInfo) return null

        return (
          <div
            className={clsx(
              `${
                !(idx % 2)
                  ? styles.indexContainer
                  : cs([styles.indexContainer, styles.isOdd])
              }`,
              'flex justify-end',
            )}
            key={indicator.symbol}
          >
            <h3 className="mr-auto flex items-center">
              {arrowUpOrDown(indicator.changePercentage) === 'up' ? (
                <HiArrowSmUp color="#18A751" />
              ) : (
                <HiArrowSmDown color="#A70202" />
              )}
              &nbsp;
              <Link
                href={`/markets/indices/${symbolInfo.url}`}
                className="text-kitco-black"
              >
                {symbolInfo.name}
              </Link>
            </h3>
            <p
              className={clsx(
                colorize(indicator.changePercentage),
                'mr-[40px] lg:mr-5',
              )}
            >
              {indicator.price.toFixed(2)}
            </p>
            <p className={colorize(indicator.changePercentage)}>
              {indicator.changePercentage.toFixed(2)}%
            </p>
          </div>
        )
      })}
    </BlockShell>
  )
}
