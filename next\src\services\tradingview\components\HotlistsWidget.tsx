'use client'

import { useEffect, useRef } from 'react'

interface HotlistsWidgetProps {
  width?: string
  height?: string
  colorTheme?: 'light' | 'dark'
  dateRange?: string
  exchange?: string
  showChart?: boolean
  locale?: string
  isTransparent?: boolean
  showSymbolLogo?: boolean
  showFloatingTooltip?: boolean
}

export default function HotlistsWidget({
  width = '100%',
  height = '500',
  colorTheme = 'light',
  dateRange = '12M',
  exchange = 'US',
  showChart = true,
  locale = 'en',
  isTransparent = false,
  showSymbolLogo = false,
  showFloatingTooltip = false,
}: HotlistsWidgetProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    const script = document.createElement('script')
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-hotlists.js'
    script.async = true
    script.innerHTML = JSON.stringify({
      colorTheme,
      dateRange,
      exchange,
      showChart,
      locale,
      largeChartUrl: `${process.env.NEXT_PUBLIC_URL}/markets/stocks/`,
      isTransparent,
      showSymbolLogo,
      showFloatingTooltip,
      width,
      height,
      plotLineColorGrowing: 'rgba(41, 98, 255, 1)',
      plotLineColorFalling: 'rgba(41, 98, 255, 1)',
      gridLineColor: 'rgba(46, 46, 46, 0)',
      scaleFontColor: 'rgba(15, 15, 15, 1)',
      belowLineFillColorGrowing: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorFalling: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorGrowingBottom: 'rgba(41, 98, 255, 0)',
      belowLineFillColorFallingBottom: 'rgba(41, 98, 255, 0)',
      symbolActiveColor: 'rgba(41, 98, 255, 0.12)',
    })

    containerRef.current.appendChild(script)

    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
    }
  }, [
    width,
    height,
    colorTheme,
    dateRange,
    exchange,
    showChart,
    locale,
    isTransparent,
    showSymbolLogo,
    showFloatingTooltip,
  ])

  return (
    <div className="tradingview-widget-container">
      <div
        className="tradingview-widget-container__widget"
        ref={containerRef}
      ></div>
      <div className="tradingview-widget-copyright">
        <a
          href="https://www.tradingview.com/"
          rel="noopener nofollow"
          target="_blank"
        >
          <span className="blue-text">Track all markets on TradingView</span>
        </a>
      </div>
    </div>
  )
}
