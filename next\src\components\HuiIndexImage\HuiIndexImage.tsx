import Image from 'next/image'
import { useState } from 'react'
import { FaSpinner } from 'react-icons/fa'
import { current } from '~/src/utils/timestamps'

const HuiIndexImage = ({ className }: { className?: string }) => {
  const [loading, setLoading] = useState<boolean>(true)

  /**
   * Handle the image finish load state
   */
  const handleImageLoaded = () => {
    setLoading(false)
  }

  return (
    <div className={`${className} overflow-hidden`}>
      <div className="relative flex items-center justify-center overflow-hidden w-full h-full">
        {loading && (
          <div className="absolute flex items-center justify-center z-10">
            <FaSpinner
              className="animate-spin text-4xl text-gray-600"
              size={52}
            />
          </div>
        )}
        <Image
          src={`https://www.weblinks247.com/indexes/idx24_hui_en_3.gif?random=${current()}`}
          alt="HUI Index"
          width={180}
          height={114}
          unoptimized={true}
          onLoad={handleImageLoaded}
          onError={handleImageLoaded}
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            width: 'auto',
            height: 'auto',
          }}
          className="object-contain"
        />
      </div>
    </div>
  )
}

export default HuiIndexImage
