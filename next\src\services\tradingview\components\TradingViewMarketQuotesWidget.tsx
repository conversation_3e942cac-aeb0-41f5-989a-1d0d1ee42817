'use client'

import { useTradingViewWidgetMount } from '../hooks/useTradingViewWidgetMount'

interface TradingViewMarketQuotesWidgetProps {
  width?: string
  height?: string
  colorTheme?: 'light' | 'dark'
  locale?: string
  isTransparent?: boolean
  showSymbolLogo?: boolean
  backgroundColor?: string
}

export default function TradingViewMarketQuotesWidget({
  width = '100%',
  height = '100%',
  colorTheme = 'light',
  locale = 'en',
  isTransparent = false,
  showSymbolLogo = false,
  backgroundColor = '#ffffff',
}: TradingViewMarketQuotesWidgetProps) {
  const config = {
    width,
    height,
    symbolsGroups: [
      {
        name: 'North America',
        originalName: 'Indices',
        symbols: [
          {
            name: 'FOREXCOM:SPXUSD',
            displayName: 'S&P 500 Index',
          },
          {
            name: 'FOREXCOM:NSXUSD',
            displayName: 'US 100 Cash CFD',
          },
          {
            name: 'FOREXCOM:DJI',
            displayName: 'Dow Jones Industrial Average Index',
          },
          {
            name: 'NASDAQ:IXIC',
            displayName: 'Nasdaq Composite Index',
          },
        ],
      },
      {
        name: 'Europe',
        symbols: [
          {
            name: 'INDEX:DEU40',
            displayName: 'DAX Index',
          },
          {
            name: 'FOREXCOM:UK100',
            displayName: 'FTSE 100 Index',
          },
          {
            name: 'ICMARKETS:F40',
            displayName: 'CAC 40 Index',
          },
          {
            name: 'SIX:SMI',
            displayName: 'Swiss Market Index',
          },
        ],
      },
      {
        name: 'Asia',
        symbols: [
          {
            name: 'INDEX:NKY',
            displayName: 'Nikkei 225 Index',
          },
          {
            name: 'HSI:HSI',
            displayName: 'Hang Seng Index',
          },
        ],
      },
      {
        name: 'Australia',
        symbols: [
          {
            name: 'FXOPEN:AUS200',
            displayName: 'ASX 200 Index',
          },
        ],
      },
    ],
    showSymbolLogo,
    isTransparent,
    colorTheme,
    locale,
    backgroundColor,
  }

  const { containerRef } = useTradingViewWidgetMount({
    scriptSrc:
      'https://s3.tradingview.com/external-embedding/embed-widget-market-quotes.js',
    config,
    dependencies: [
      width,
      height,
      colorTheme,
      locale,
      isTransparent,
      showSymbolLogo,
      backgroundColor,
    ],
  })

  return (
    <div className="tradingview-widget-container">
      <div
        className="tradingview-widget-container__widget"
        ref={containerRef}
      ></div>
      <div className="tradingview-widget-copyright">
        <a
          href="https://www.tradingview.com/"
          rel="noopener nofollow"
          target="_blank"
        >
          <span className="blue-text">Track all markets on TradingView</span>
        </a>
      </div>
    </div>
  )
}
