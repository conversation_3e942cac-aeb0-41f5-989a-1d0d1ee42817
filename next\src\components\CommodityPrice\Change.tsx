import type { FC, PropsWithChildren } from 'react'
import styles from '~/src/components/CommodityPrice/CommodityPrice.module.scss'
import cs from '~/src/utils/cs'

/**
 * ChangeProps interface.
 */
interface ChangeProps extends PropsWithChildren {
  styleUpOrDown: string
}

/**
 * Change component.
 *
 * @param children
 * @param styleUpOrDown
 * @constructor
 */
const Change: FC<ChangeProps> = ({ children, styleUpOrDown }: ChangeProps) => {
  return (
    <p
      className={cs([
        styleUpOrDown,
        styles.convertPrice,
        'min-w-[82px] justify-self-end',
      ])}
    >
      {children}
    </p>
  )
}

export default Change
