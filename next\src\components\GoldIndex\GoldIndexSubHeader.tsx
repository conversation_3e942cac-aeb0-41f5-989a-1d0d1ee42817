import Image from 'next/image'
import type { FC } from 'react'
import Price from '~/src/components/Price/Price'
import { useCommodity } from '~/src/contexts/CommodityContext'
import useGoldIndexMessages from '~/src/hooks/GlobalIndex/useGlobalIndexMessages'
import type CommodityData from '~/src/types/DataTable/CommodityData'

interface GoldIndexSubHeaderProps {
  data: CommodityData[]
}

const GoldIndexSubHeader: FC<GoldIndexSubHeaderProps> = ({
  data,
}: GoldIndexSubHeaderProps) => {
  const { toggleAutoRotate, isAutoRotating } = useCommodity()

  // Get the question and explanation messages for the selected commodity
  const { getQuestionMessage, getExplanationMessage, commodityData } =
    // useGoldIndexMessages(data, 'Gold')
    useGoldIndexMessages(data)

  return (
    // <div className="mt-9 grid grid-cols-1 items-center justify-end gap-8 md:grid-cols-5">
    //   <div className="inline-flex shrink grow basis-0 flex-row items-start justify-center gap-4 self-stretch rounded-lg bg-stone-50 px-6 py-4 md:col-span-3 md:flex-col">
    //     <div className="inline-flex items-center justify-start gap-4 self-stretch">

    <div
      className="mt-9 grid grid-cols-1 items-center justify-end gap-8 md:grid-cols-5 cursor-pointer"
      onClick={toggleAutoRotate}
      title={isAutoRotating ? 'Pause rotation' : 'Resume rotation'}
    >
      <div className="inline-flex shrink grow basis-0 flex-row items-start justify-center gap-4 self-stretch rounded-lg bg-stone-50 px-6 py-4 md:col-span-3 md:flex-col hover:bg-stone-100 transition-colors relative">
        <div className="inline-flex items-center justify-start gap-4 self-stretch mt-2">
          <div className="flex items-center justify-center">
            {/* <Image src="/icons/coins.svg" alt="Coins" width={24} height={25} /> */}
            <Image
              src="/icons/coins.svg"
              alt="Coins"
              width={24}
              height={25}
              className={isAutoRotating ? 'animate-pulse' : ''}
            />
          </div>
          <div className="inline-flex shrink grow basis-0 flex-col items-start justify-end gap-2">
            <div className="text-center font-['Mulish'] text-sm font-bold leading-none text-neutral-900">
              {getQuestionMessage()}
            </div>
            <div className="self-stretch font-['Mulish'] text-sm font-normal leading-relaxed text-neutral-700">
              {getExplanationMessage()}
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-full items-start justify-between md:col-span-2">
        <div className="inline-flex shrink grow basis-0 flex-col items-start justify-start gap-2">
          <div className="inline-flex items-start justify-between self-stretch border-b border-neutral-400 pb-2">
            <div className="text-left font-['Mulish'] text-sm font-semibold leading-none text-neutral-900">
              {(commodityData?.changeDueToUSD?.changeVal ?? 0) >= 0
                ? 'Change due to Weakening of USD'
                : 'Decrease due to Strengthening of USD'}
            </div>
            <div className="flex h-4 items-center justify-end">
              <div className="flex h-3.5 w-3.5 origin-top-left rotate-180 items-center justify-center px-1" />
              <Price
                price={commodityData?.changeDueToUSD?.change}
                priceVal={commodityData?.changeDueToUSD?.changeVal}
                symbol=""
              />
            </div>
          </div>
          <div className="inline-flex items-start justify-between self-stretch border-b border-neutral-400 pb-2">
            <div className="text-left font-['Mulish'] text-sm font-semibold leading-none text-neutral-900">
              Change due to Predominant{' '}
              {(commodityData?.changeDueToTrade?.changeVal ?? 0) >= 0
                ? 'Buyers'
                : 'Sellers'}
            </div>
            <div className="flex h-4 items-center justify-end">
              <div className="flex h-3.5 w-3.5 origin-top-left rotate-180 items-center justify-center px-1" />
              <Price
                price={commodityData?.changeDueToTrade?.change}
                priceVal={commodityData?.changeDueToTrade?.changeVal}
                symbol=""
              />
            </div>
          </div>
          <div className="inline-flex items-start justify-between self-stretch">
            <div className="text-left font-['Mulish'] text-sm font-semibold leading-none text-neutral-900">
              Total change
            </div>
            <div className="flex items-center justify-end">
              <div className="flex h-3.5 w-3.5 origin-top-left rotate-180 items-center justify-center px-1" />
              <Price
                price={commodityData?.totalChange?.change}
                priceVal={commodityData?.totalChange?.changeVal}
                symbol=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GoldIndexSubHeader
