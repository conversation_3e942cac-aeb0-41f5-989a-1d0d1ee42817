import type { KitcoValue } from '../types/KitcoValue'
import { parseXmlResponse } from '../utils/parseXmlResponse'

/**
 * Other indices symbols for market indicators
 */
export const OTHER_INDICES_SYMBOLS = [
  'DJI',
  'IXIC',
  'SPX',
  'NYA',
  'N225',
  'GSPTSE',
] as const

/**
 * Fetches other indices data from Kitco proxy
 * URL: https://proxy.kitco.com/getValue?ver=2.0&symbol=DJI,IXIC,SPX,NYA,N225,GSPTSE&type=xml
 *
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to array of other indices data
 */
export async function fetchOtherIndicesData(
  version = '2.0',
): Promise<KitcoValue[]> {
  try {
    // Build the Kitco proxy URL directly
    const symbols = OTHER_INDICES_SYMBOLS.join(',')
    const url = `https://proxy.kitco.com/getValue?ver=${version}&symbol=${symbols}&type=xml`

    // Fetch data directly from Kitco proxy
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'text/plain, text/xml',
        'User-Agent': 'Kitco-CMS-Next/1.0',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`)
    }

    // Get the XML response as text
    const responseText = await response.text()
    console.log('Raw response from Kitco proxy (other indices):', responseText)

    if (!responseText) {
      throw new Error('Empty response from API')
    }

    // Parse the XML response
    const parsedResponse = await parseXmlResponse(responseText)
    console.log('Parsed other indices data:', parsedResponse.values)

    if (!parsedResponse.values || parsedResponse.values.length === 0) {
      throw new Error('Failed to parse any data from XML response')
    }

    // Filter to only the symbols we want
    const otherIndicesData = parsedResponse.values.filter((value) =>
      OTHER_INDICES_SYMBOLS.includes(value.symbol as any),
    )

    if (otherIndicesData.length === 0) {
      throw new Error('No matching symbols found in response')
    }

    return otherIndicesData
  } catch (error) {
    console.error('Error fetching other indices data:', error)
    console.error(
      'Kitco URL attempted:',
      `https://proxy.kitco.com/getValue?ver=${version}&symbol=${OTHER_INDICES_SYMBOLS.join(',')}&type=xml`,
    )

    // More detailed error logging
    if (
      error instanceof TypeError &&
      error.message.includes('Failed to fetch')
    ) {
      throw new Error('Network error - unable to reach Kitco proxy')
    } else if (error instanceof Error) {
      throw new Error(`API Error: ${error.message}`)
    } else {
      throw new Error('Unknown error occurred while fetching data')
    }
  }
}

/**
 * Fetches a specific symbol from the other indices data
 *
 * @param symbol - Symbol to fetch (DJI, IXIC, SPX, NYA, N225, or GSPTSE)
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to specific symbol data or null if not found
 */
export async function fetchOtherIndexBySymbol(
  symbol: 'DJI' | 'IXIC' | 'SPX' | 'NYA' | 'N225' | 'GSPTSE',
  version = '2.0',
): Promise<KitcoValue | null> {
  const allData = await fetchOtherIndicesData(version)
  return allData.find((item) => item.symbol === symbol) || null
}
