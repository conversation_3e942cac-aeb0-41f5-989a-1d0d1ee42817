import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const news: SectionItems[] = [
  {
    name: 'Mining News',
    href: '/news/category/mining',
    as: '/news/category/mining',
  },
]

export const capital: SectionItems[] = [
  {
    name: 'Kitco Gibson Capital',
    href: 'https://www.kitcogibson.com/KGCapital/Home',
  },
]

export const stocks: SectionItems[] = [
  { name: 'XAU', href: '/mining/[name]', as: '/mining/XAU' },
  { name: 'HUI', href: '/mining/[name]', as: '/mining/HUI' },
  { name: 'JSE Gold', href: '/mining/[name]', as: '/mining/JSE' },
  { name: 'TSX Gold', href: '/mining/[name]', as: '/mining/TSX' },
]

function MiningMenu() {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList
          title="Mining Equities"
          titleUrl="/mining/mining-equities"
        />
        <SectionList title="Mining News" titleUrl="/news/category/mining" />
        <SectionList title="Press Releases" titleUrl="/mining/press-release" />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default MiningMenu
