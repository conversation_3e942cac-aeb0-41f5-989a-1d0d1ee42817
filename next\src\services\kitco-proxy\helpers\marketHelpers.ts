import {
  fetchMultipleSymbolsData,
  fetchSymbolData,
} from '../api/fetchKitcoData'
import type { KitcoValue } from '../types/KitcoValue'

/**
 * Common market symbols
 */
export const MARKET_SYMBOLS = {
  SPX: 'SPX', // S&P 500
  DJI: 'DJI', // Dow Jones Industrial Average
  CL: 'CL', // Crude Oil
} as const

/**
 * Fetches S&P 500 data specifically
 *
 * @param version - API version (optional)
 * @returns Promise resolving to SPX data or null if not found
 */
export async function fetchSPXData(
  version?: string,
): Promise<KitcoValue | null> {
  return fetchSymbolData(MARKET_SYMBOLS.SPX, version)
}

/**
 * Fetches Dow Jones data specifically
 *
 * @param version - API version (optional)
 * @returns Promise resolving to DJI data or null if not found
 */
export async function fetchDJIData(
  version?: string,
): Promise<KitcoValue | null> {
  return fetchSymbolData(MARKET_SYMBOLS.DJI, version)
}

/**
 * Fetches Crude Oil data specifically
 *
 * @param version - API version (optional)
 * @returns Promise resolving to CL data or null if not found
 */
export async function fetchCrudeOilData(
  version?: string,
): Promise<KitcoValue | null> {
  return fetchSymbolData(MARKET_SYMBOLS.CL, version)
}

/**
 * Fetches all major market indices data in a single request
 *
 * @param version - API version (optional)
 * @returns Promise resolving to an object with market data
 */
export async function fetchMarketIndicesData(version?: string): Promise<{
  spx: KitcoValue | null
  dji: KitcoValue | null
  cl: KitcoValue | null
}> {
  const symbolsData = await fetchMultipleSymbolsData(
    [MARKET_SYMBOLS.SPX, MARKET_SYMBOLS.DJI, MARKET_SYMBOLS.CL],
    version,
  )

  return {
    spx: symbolsData.find((d) => d.symbol === MARKET_SYMBOLS.SPX) || null,
    dji: symbolsData.find((d) => d.symbol === MARKET_SYMBOLS.DJI) || null,
    cl: symbolsData.find((d) => d.symbol === MARKET_SYMBOLS.CL) || null,
  }
}
