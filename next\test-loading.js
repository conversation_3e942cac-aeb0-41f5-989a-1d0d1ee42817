// Test script to verify KGX table loading screen
// Run this in the browser console on the kitco-gold-index page

console.log('=== KGX Table Loading Screen Test ===');

// Function to simulate loading state by temporarily hiding data
window.testKGXLoading = function() {
  console.log('Testing KGX loading screen...');
  
  // Find the KGX table container
  const tableContainer = document.querySelector('[data-testid="kgx-table"], .space-y-4, table');
  
  if (tableContainer) {
    // Temporarily hide the table to trigger loading state
    tableContainer.style.display = 'none';
    
    console.log('✓ Table hidden - loading state should appear');
    
    // Restore after 3 seconds
    setTimeout(() => {
      tableContainer.style.display = '';
      console.log('✓ Table restored');
    }, 3000);
  } else {
    console.log('✗ Could not find KGX table container');
  }
};

// Function to check if loading elements exist
window.checkLoadingElements = function() {
  console.log('Checking for loading elements...');
  
  const loadingOverlay = document.querySelector('.absolute.inset-0.bg-white\\/60');
  const loadingSpinner = document.querySelector('.animate-spin');
  const loadingText = document.querySelector('span:contains("Loading commodity data")');
  const skeletonElements = document.querySelectorAll('.animate-pulse');
  
  console.log('Loading overlay found:', !!loadingOverlay);
  console.log('Loading spinner found:', !!loadingSpinner);
  console.log('Loading text found:', !!loadingText);
  console.log('Skeleton elements found:', skeletonElements.length);
  
  return {
    overlay: !!loadingOverlay,
    spinner: !!loadingSpinner,
    text: !!loadingText,
    skeletons: skeletonElements.length
  };
};

// Function to force refresh data (if possible)
window.refreshKGXData = function() {
  console.log('Attempting to refresh KGX data...');
  // This will trigger a page reload which should show loading state
  window.location.reload();
};

console.log('\nAvailable test functions:');
console.log('- testKGXLoading() - Simulate loading by hiding table');
console.log('- checkLoadingElements() - Check if loading elements exist');
console.log('- refreshKGXData() - Refresh page to see initial loading');

// Auto-check for loading elements
checkLoadingElements();
