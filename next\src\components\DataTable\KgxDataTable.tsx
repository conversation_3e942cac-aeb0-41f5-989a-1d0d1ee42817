import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  type TableOptions,
  useReactTable,
} from '@tanstack/react-table'
import clsx from 'clsx'
import { startTransition, useEffect, useMemo, useState } from 'react'
import { FaCaretDown, FaCaretUp, FaSort } from 'react-icons/fa'
import DataTablePagination from '~/src/components/DataTable/Pagination/DataTablePagination'
import { getCommonPinningStyles } from '~/src/components/DataTable/Styles/DataTablePinningStyles'
import { DraggableRow } from '~/src/components/GoldIndex/DataTable/DragHandle'

// Helper function to get category icon with individual size and alignment control
const getCategoryIcon = (categoryName: string) => {
  switch (categoryName) {
    case 'PRECIOUS METALS':
      return (
        <div className="flex justify-left items-left">
          <img
            src="/icons/KGX-Ingots.svg"
            alt="Precious Metals"
            className="w-6 h-6"
          />
        </div>
      )
    case 'BASE METALS':
      return (
        <div className="flex justify-left items-left">
          <img
            src="/icons/KGX-Anvil.svg"
            alt="Base Metals"
            className="w-8 h-8"
          />
        </div>
      )
    case 'ENERGY':
      return (
        <div className="flex justify-left items-left">
          <img src="/icons/KGX-Oil.svg" alt="Energy" className="w-5 h-6" />
        </div>
      )
    case 'CRYPTOCURRENCIES':
      return (
        <div className="flex justify-left items-left">
          <img
            src="/icons/KGX-Crypto.svg"
            alt="Cryptocurrencies"
            className="w-5 h-6"
          />
        </div>
      )
    default:
      return (
        <div className="flex justify-left items-left">
          <span className="w-4 h-4 rounded-full bg-gray-400" />
        </div>
      )
  }
}

interface KgxDataTableProps<T extends object> {
  data: T[]
  columns: ColumnDef<T, any>[]
  extraConfig?: Partial<TableOptions<T>>
  isLoading?: boolean
  scrollOnDesktop?: boolean
  paginationEnabled?: boolean
  paginationClassName?: string
  onReorder?: (newData: T[]) => void
  disableDragDrop?: boolean
  categories?: string[]
  activeCategory?: string
  onCategoryChange?: (category: string) => void
  groupByCategory?: boolean
  categoryOrder?: string[]
  commodityCategories?: Record<string, string[]>
}

const KgxDataTable = <T extends object>({
  data,
  columns,
  extraConfig,
  scrollOnDesktop = false,
  paginationEnabled = false,
  isLoading,
  paginationClassName,
  // onReorder,
  disableDragDrop = false,
  groupByCategory = false,
  categoryOrder = [],
  commodityCategories = {},
}: KgxDataTableProps<T>) => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    startTransition(() => {
      setIsClient(true)
    })
  }, [])

  const displayData = useMemo(() => {
    if (!data || data.length === 0) return []

    return data.map((item: any) => ({
      ...item,
      _rowId:
        item.commodity || item.id || item.Symbol || Math.random().toString(36),
    }))
  }, [data])

  // Group data by category if groupByCategory is enabled
  const groupedData = useMemo(() => {
    if (!groupByCategory || !commodityCategories) {
      return { ungrouped: displayData }
    }

    const groups: Record<string, typeof displayData> = {}

    // Initialize groups for each category in the specified order
    categoryOrder.forEach((category) => {
      groups[category] = []
    })

    // Group items by category
    displayData.forEach((item: any) => {
      const commodity = item.commodity
      let categoryFound = false

      for (const [categoryName, commodities] of Object.entries(
        commodityCategories,
      )) {
        if (commodities.includes(commodity)) {
          if (!groups[categoryName]) {
            groups[categoryName] = []
          }
          groups[categoryName].push(item)
          categoryFound = true
          break
        }
      }

      // If no category found, add to ungrouped
      if (!categoryFound) {
        if (!groups.ungrouped) {
          groups.ungrouped = []
        }
        groups.ungrouped.push(item)
      }
    })

    return groups
  }, [displayData, groupByCategory, commodityCategories, categoryOrder])

  const table = useReactTable({
    data: displayData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    ...(extraConfig || {}),
  })

  // Create category tables at the top level to avoid hooks violations
  // We need to create a table for each possible category to avoid conditional hook calls
  const preciousMetalsTable = useReactTable({
    data: groupByCategory ? groupedData['PRECIOUS METALS'] || [] : [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    ...(extraConfig || {}),
  })

  const baseMetalsTable = useReactTable({
    data: groupByCategory ? groupedData['BASE METALS'] || [] : [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    ...(extraConfig || {}),
  })

  const energyTable = useReactTable({
    data: groupByCategory ? groupedData['ENERGY'] || [] : [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    ...(extraConfig || {}),
  })

  const cryptocurrenciesTable = useReactTable({
    data: groupByCategory ? groupedData['CRYPTOCURRENCIES'] || [] : [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    ...(extraConfig || {}),
  })

  // Map category names to their respective tables
  const categoryTables = useMemo(
    () => ({
      'PRECIOUS METALS': preciousMetalsTable,
      'BASE METALS': baseMetalsTable,
      ENERGY: energyTable,
      CRYPTOCURRENCIES: cryptocurrenciesTable,
    }),
    [preciousMetalsTable, baseMetalsTable, energyTable, cryptocurrenciesTable],
  )

  const [selectedRowId, setSelectedRowId] = useState<string | null>(null)

  const handleRowClick = (rowId: string) => {
    startTransition(() => {
      setSelectedRowId(rowId === selectedRowId ? null : rowId)
    })
  }

  useEffect(() => {
    if (table.getState().pagination.pageSize) {
      startTransition(() => {
        table.setPageSize(table.getState().pagination.pageSize)
      })
    }
  }, [extraConfig])

  // Enhanced loading skeleton component
  const LoadingSkeleton = ({
    width = '100%',
    height = '16px',
    className = '',
  }) => (
    <div
      className={`animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] rounded-sm ${className}`}
      style={{
        width,
        height,
        animation: 'shimmer 1.5s ease-in-out infinite',
      }}
    />
  )

  // Render loading state within table structure
  const renderLoadingTable = () => {
    if (!groupByCategory) {
      return (
        <div className="relative" data-testid="kgx-table-loading">
          {/* Subtle loading overlay */}
          <div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] z-10 flex items-center justify-center transition-all duration-300">
            <div className="flex items-center space-x-3 bg-white/95 px-6 py-3 rounded-xl shadow-lg border border-gray-200/50 backdrop-blur-sm">
              <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="text-sm text-gray-700 font-medium">
                Loading commodity data...
              </span>
            </div>
          </div>

          <table className="table-auto w-full">
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className="h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 pl-4 text-left"
                    >
                      <div className="flex cursor-pointer select-none items-center gap-2 text-xs font-bold leading-tight text-zinc-600">
                        {header.isPlaceholder
                          ? null
                          : typeof header.column.columnDef.header === 'string'
                            ? header.column.columnDef.header
                            : header.column.id === 'commodity'
                              ? 'Commodities'
                              : header.column.id === 'lastBid'
                                ? 'Last (Bid)'
                                : header.column.id === 'changeDueToUSD'
                                  ? 'Change due to USD performance'
                                  : header.column.id === 'changeDueToTrade'
                                    ? 'Change due to normal trading'
                                    : header.column.id === 'totalChange'
                                      ? 'Total change'
                                      : 'Loading...'}
                      </div>
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {/* Render skeleton rows to maintain layout */}
              {Array.from({ length: 8 }).map((_, index) => (
                <tr key={index} className="border-b border-gray-100">
                  {columns.map((_, colIndex) => (
                    <td key={colIndex} className="px-2 pl-4 py-3">
                      <LoadingSkeleton
                        width={colIndex === 0 ? '80px' : '60px'}
                        height="14px"
                        className="opacity-60"
                      />
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )
    }

    // For grouped tables, show loading within each category
    return (
      <div className="relative" data-testid="kgx-table-loading-grouped">
        {/* Subtle loading overlay for grouped tables */}
        <div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] z-10 flex items-center justify-center transition-all duration-300">
          <div className="flex items-center space-x-3 bg-white/95 px-6 py-3 rounded-xl shadow-lg border border-gray-200/50 backdrop-blur-sm">
            <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="text-sm text-gray-700 font-medium">
              Loading commodity data...
            </span>
          </div>
        </div>

        <div className="space-y-4">
          {categoryOrder.map((categoryName) => (
            <table key={categoryName} className="table-auto w-full">
              <thead>
                <tr>
                  <th
                    colSpan={columns.length + (disableDragDrop ? 0 : 1)}
                    className={clsx(
                      'h-11 max-h-11 px-1 pl-0 text-left',
                      'text-md font-bold',
                      'border-b border-black',
                    )}
                  >
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(categoryName)}
                      <span>{categoryName}</span>
                      {categoryName === 'CRYPTOCURRENCIES' && (
                        <span className="ml-2 inline-flex items-center rounded-md bg-ktc-blue px-1 py-0.5 text-xs font-medium text-white group-hover:bg-kitco-black">
                          NEW
                        </span>
                      )}
                    </div>
                  </th>
                </tr>
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      className="h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 pl-4 text-left"
                    >
                      <div className="flex cursor-pointer select-none items-center gap-2 text-xs font-bold leading-tight text-zinc-600">
                        {typeof column.header === 'string'
                          ? column.header
                          : column.id === 'commodity'
                            ? 'Commodities'
                            : column.id === 'lastBid'
                              ? 'Last (Bid)'
                              : column.id === 'changeDueToUSD'
                                ? 'Change due to USD performance'
                                : column.id === 'changeDueToTrade'
                                  ? 'Change due to normal trading'
                                  : column.id === 'totalChange'
                                    ? 'Total change'
                                    : 'Loading...'}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {/* Render skeleton rows for each category */}
                {Array.from({ length: Math.min(4, categoryOrder.length) }).map(
                  (_, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      {columns.map((_, colIndex) => (
                        <td key={colIndex} className="px-2 pl-4 py-3">
                          <LoadingSkeleton
                            width={colIndex === 0 ? '80px' : '60px'}
                            height="14px"
                            className="opacity-60"
                          />
                        </td>
                      ))}
                    </tr>
                  ),
                )}
              </tbody>
            </table>
          ))}
        </div>
      </div>
    )
  }

  if (isLoading) {
    return renderLoadingTable()
  }

  // Render grouped table structure
  const renderGroupedTable = () => {
    return (
      <div className="space-y-4" data-testid="kgx-table-loaded">
        {categoryOrder.map((categoryName) => {
          const categoryData = groupedData[categoryName] || []
          if (categoryData.length === 0) return null

          // Use the pre-created table for this category
          const categoryTable =
            categoryTables[categoryName as keyof typeof categoryTables]

          return (
            <table key={categoryName} className={`table-auto w-full`}>
              {/* Category Header */}
              <thead>
                <tr>
                  <th
                    colSpan={columns.length + (disableDragDrop ? 0 : 1)}
                    className={clsx(
                      'h-11 max-h-11 px-1 pl-0 text-left',
                      'text-md font-bold',
                      'border-b border-black',
                    )}
                  >
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(categoryName)}
                      <span>{categoryName}</span>
                      {categoryName === 'CRYPTOCURRENCIES' && (
                        <span className="ml-2 inline-flex items-center rounded-md bg-ktc-blue px-1 py-0.5 text-xs font-medium text-white group-hover:bg-kitco-black">
                          NEW
                        </span>
                      )}
                    </div>
                  </th>
                </tr>
                {/* Column Headers */}
                {categoryTable.getHeaderGroups().map((headerGroup) => (
                  <tr key={`${categoryName}-${headerGroup.id}`}>
                    {headerGroup.headers.map((header) => {
                      const { column } = header
                      const headerMeta: any = header.column.columnDef.meta
                      const headerClassName = headerMeta?.classNameHeader

                      return (
                        <th
                          key={header.id}
                          colSpan={header.colSpan}
                          style={{ ...getCommonPinningStyles(column, true) }}
                          className={clsx(
                            'h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 pl-4 text-left',
                            headerClassName,
                          )}
                        >
                          <div
                            className={clsx(
                              'flex cursor-pointer select-none items-center gap-2 text-xs font-bold leading-tight text-zinc-600',
                              header.column.getCanSort()
                                ? 'cursor-pointer select-none'
                                : '',
                              headerMeta?.classNameHeaderDiv,
                            )}
                            onClick={header.column.getToggleSortingHandler()}
                            onKeyDown={header.column.getToggleSortingHandler()}
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}{' '}
                            {header.column.getIsSorted() ? (
                              {
                                asc: <FaCaretUp />,
                                desc: <FaCaretDown />,
                              }[header.column.getIsSorted() as string]
                            ) : header.column.getCanSort() ? (
                              <FaSort />
                            ) : null}
                          </div>
                        </th>
                      )
                    })}
                    {!disableDragDrop && (
                      <th className="w-0 px-0 bg-transparent border-b-0 opacity-0 invisible" />
                    )}
                  </tr>
                ))}
              </thead>
              {/* Category Data */}
              <tbody>
                {categoryTable.getRowModel().rows.map((row) =>
                  !disableDragDrop && isClient ? (
                    <DraggableRow
                      key={row.id}
                      id={
                        (row.original as any).commodity ||
                        (row.original as any)._rowId
                      }
                    >
                      {row.getVisibleCells().map((cell) => {
                        const { column } = cell
                        const headerMeta: any = cell.column.columnDef.meta
                        const cellClassName = headerMeta?.classNameCell

                        return (
                          <td
                            key={cell.id}
                            style={{ ...getCommonPinningStyles(column, false) }}
                            className={clsx('px-4', cellClassName)}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </td>
                        )
                      })}
                    </DraggableRow>
                  ) : (
                    <tr
                      key={row.id}
                      className={clsx(
                        'border-b border-slate-200 hover:bg-[#F8F8F8]',
                        row.id === selectedRowId ? 'bg-[#EFEFEF]' : 'bg-white',
                      )}
                      onClick={() => handleRowClick(row.id)}
                      onKeyDown={() => handleRowClick(row.id)}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const { column } = cell
                        const headerMeta: any = cell.column.columnDef.meta
                        const cellClassName = headerMeta?.classNameCell

                        return (
                          <td
                            key={cell.id}
                            style={{ ...getCommonPinningStyles(column, false) }}
                            className={clsx('px-4', cellClassName)}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </td>
                        )
                      })}
                    </tr>
                  ),
                )}
              </tbody>
            </table>
          )
        })}
      </div>
    )
  }

  // Render standard table structure
  const renderStandardTable = () => {
    return (
      <table className={`table-auto w-full`}>
        <thead>
          {/* Column Headers */}
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const { column } = header
                const headerMeta: any = header.column.columnDef.meta
                const headerClassName = headerMeta?.classNameHeader

                return (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{ ...getCommonPinningStyles(column, true) }}
                    className={clsx(
                      'h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 pl-4 text-left',
                      headerClassName,
                    )}
                  >
                    <div
                      className={clsx(
                        'flex cursor-pointer select-none items-center gap-2 text-xs font-bold leading-tight text-zinc-600',
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none'
                          : '',
                        headerMeta?.classNameHeaderDiv,
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                      onKeyDown={header.column.getToggleSortingHandler()}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}{' '}
                      {header.column.getIsSorted() ? (
                        {
                          asc: <FaCaretUp />,
                          desc: <FaCaretDown />,
                        }[header.column.getIsSorted() as string]
                      ) : header.column.getCanSort() ? (
                        <FaSort />
                      ) : null}
                    </div>
                  </th>
                )
              })}
              {!disableDragDrop && (
                <th className="w-0 px-0 bg-transparent border-b-0 opacity-0 invisible" />
              )}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) =>
            !disableDragDrop && isClient ? (
              <DraggableRow
                key={row.id}
                id={
                  (row.original as any).commodity ||
                  (row.original as any)._rowId
                }
              >
                {row.getVisibleCells().map((cell) => {
                  const { column } = cell
                  const headerMeta: any = cell.column.columnDef.meta
                  const cellClassName = headerMeta?.classNameCell

                  return (
                    <td
                      key={cell.id}
                      style={{ ...getCommonPinningStyles(column, false) }}
                      className={clsx('px-4', cellClassName)}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  )
                })}
              </DraggableRow>
            ) : (
              <tr
                key={row.id}
                className={clsx(
                  'border-b border-slate-200 hover:bg-[#F8F8F8]',
                  row.id === selectedRowId ? 'bg-[#EFEFEF]' : 'bg-white',
                )}
                onClick={() => handleRowClick(row.id)}
                onKeyDown={() => handleRowClick(row.id)}
              >
                {row.getVisibleCells().map((cell) => {
                  const { column } = cell
                  const headerMeta: any = cell.column.columnDef.meta
                  const cellClassName = headerMeta?.classNameCell

                  return (
                    <td
                      key={cell.id}
                      style={{ ...getCommonPinningStyles(column, false) }}
                      className={clsx('px-4', cellClassName)}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  )
                })}
              </tr>
            ),
          )}
        </tbody>
      </table>
    )
  }

  return (
    <div
      className={clsx(
        'w-full overflow-x-auto',
        scrollOnDesktop ? '' : 'lg:overflow-x-hidden',
      )}
    >
      {groupByCategory ? renderGroupedTable() : renderStandardTable()}
      {paginationEnabled && (
        <div className={paginationClassName}>
          <DataTablePagination
            table={table}
            setPageSize={table.setPageSize}
            setPageIndex={table.setPageIndex}
          />
        </div>
      )}
    </div>
  )
}

export default KgxDataTable
