import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const cryptos: SectionItems[] = [
  {
    name: 'Bitcoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/bitcoin',
  },
  {
    name: 'Ethereum',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ethereum',
  },
  {
    name: 'Litecoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/litecoin',
  },
  {
    name: 'Mon<PERSON>',
    href: '/price/crypto/[name]',
    as: '/price/crypto/monero',
  },
  {
    name: 'Ripple',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ripple',
  },
  {
    name: 'Dash',
    href: '/price/crypto/[name]',
    as: '/price/crypto/dash',
  },
]

export const more: SectionItems[] = [
  { name: 'Metals', href: '/markets/metals' },
  { name: 'Stocks Gainers and Losers', href: '/markets/stocks' },
]

function MarketsMenu() {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList title="Markets Overview" titleUrl="/markets" />
      </Navigation.SubMenuColumn>
      <Navigation.SubMenuColumn>
        <SectionList title="Forex" titleUrl="/price/forex" />
      </Navigation.SubMenuColumn>
      <Navigation.SubMenuColumn>
        <SectionList
          title="Kitco Global Index"
          titleUrl="/markets/kitco-gold-index"
        />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default MarketsMenu
