import clsx from 'clsx'
import { useRouter } from 'next/router'
import type { FC } from 'react'
import CommodityPriceItem from '~/src/components/CommodityPrice/CommodityPriceItem'
import type { MetalQuoteQuery } from '~/src/generated'
import { useCommodityPrice } from '~/src/hooks/CommodityPrice/useCommodityPrice'
import type CommodityPriceItemValues from '~/src/types/CommodityPriceItemValues'
import type Currency from '~/src/types/Currency'
import cs from '~/src/utils/cs'
import { CurrencySelect } from '../CurrencySelect'
import styles from './CommodityPrice.module.scss'

/**
 * Props for the CommodityPrice component
 */
interface CommodityPriceProps {
  currency: Currency
  data: MetalQuoteQuery
  symbol: string
  isBaseMetal?: boolean
}

/**
 * CommodityPrice component.
 *
 * This component is used to display the commodity price.
 *
 * @param currency
 * @param data
 * @param symbol
 * @param isBaseMetal
 * @constructor
 */
const CommodityPrice: FC<CommodityPriceProps> = ({
  currency,
  data,
  symbol,
  isBaseMetal = false,
}: CommodityPriceProps) => {
  // Get the data from the useCommodityPrice hook
  const { isUp, displayNullOrValue } = useCommodityPrice({
    data,
    currency,
    symbol,
    isBaseMetal,
  })

  // Get the today price
  const priceToday = data?.GetMetalQuoteV3?.results?.[0]

  // Get the router and page name
  const router = useRouter()
  const { name: pageName } = router.query

  // Get the up or down style
  const styleUpOrDown = isUp ? cs([styles.up]) : cs([styles.down])

  const renderCommodityPriceItems = () => {
    const items: CommodityPriceItemValues[] = isBaseMetal
      ? [
          {
            label: 'pound',
            valueKey: 'convert_price_pound_bmt',
            changeKey: 'change',
          },
          {
            label: 'gram',
            valueKey: 'convert_price_gram_bmt',
            changeKey: 'convert_change_gram_bmt',
          },
          {
            label: 'Kilo',
            valueKey: 'convert_price_kilo_bmt',
            changeKey: 'convert_change_kilo_bmt',
          },
          {
            label: 'ton',
            valueKey: 'convert_price_ton_bmt',
            changeKey: 'convert_change_ton_bmt',
          },
          {
            label: 'tonne',
            valueKey: 'convert_price_tonne_bmt',
            changeKey: 'convert_change_tonne_bmt',
          },
        ]
      : [
          { label: 'ounce', valueKey: 'bid', changeKey: 'change' },
          {
            label: 'gram',
            valueKey: 'convert_price_gram',
            changeKey: 'convert_change_gram',
          },
          {
            label: 'Kilo',
            valueKey: 'convert_price_kilo',
            changeKey: 'convert_change_kilo',
          },
          {
            label: 'pennyweight',
            valueKey: 'convert_price_penny',
            changeKey: 'convert_change_penny',
          },
          {
            label: 'tola',
            valueKey: 'convert_price_tola',
            changeKey: 'convert_change_tola',
          },
          {
            label: 'tael',
            valueKey: 'convert_price_tael',
            changeKey: 'convert_change_tael',
          },
        ]

    // Iterate over the items and return the CommodityPriceItem component
    return items.map((item) => (
      <CommodityPriceItem
        key={item.label}
        label={item.label}
        value={displayNullOrValue(item.valueKey)}
        change={displayNullOrValue(item.changeKey)}
        isUp={isUp}
      />
    ))
  }

  return (
    <>
      <div className="border-b border-ktc-borders">
        <div className="mb-px ml-0.5 pt-[10px] text-[13px] font-normal">
          Bid
        </div>
        <div className="mb-2 text-right">
          <h3 className="font-mulish mb-[3px] text-4xl font-bold leading-normal tracking-[1px]">
            {isBaseMetal
              ? displayNullOrValue('bidBM')
              : displayNullOrValue('bid')}
          </h3>
          <div className="absolute right-[15px] top-[-15px] bg-white">
            <CurrencySelect classNamesListbox={styles.listbox} />
          </div>
          <div className={clsx(styles.currencyChangeDate, 'mb-4 mr-0.5')}>
            <span className={clsx(styleUpOrDown, 'text-[15px]')}>
              {isUp ? '+' : ''}
              {displayNullOrValue('change')}&nbsp;
            </span>
            <span className={clsx(styleUpOrDown, 'text-[15px]')}>
              ({isUp ? '+' : ''}
              {displayNullOrValue('change_percentage')})
            </span>
          </div>
        </div>
      </div>
      <div className="mt-2">
        <div className="mb-10 flex items-center justify-between">
          <div className="text-sm font-normal">Ask</div>
          <div className="mr-0.5 text-[19px] font-normal">
            {isBaseMetal
              ? displayNullOrValue('askBM')
              : displayNullOrValue('ask')}
          </div>
        </div>
        <div className={pageName !== 'rhodium' ? 'mb-10' : ''}>
          <ul className={styles.spotPriceGrid}>
            {renderCommodityPriceItems()}
          </ul>
        </div>
        {pageName !== 'rhodium' && priceToday && (
          <>
            <div className={styles.priceToday}>
              <div>{priceToday?.low?.toFixed(isBaseMetal ? 4 : 2)}</div>
              <div>{priceToday?.high?.toFixed(isBaseMetal ? 4 : 2)}</div>
            </div>
            <div className="pt-2 text-center text-[14px] font-normal leading-[18px]">
              Day&apos;s Range
            </div>
          </>
        )}
      </div>
    </>
  )
}

export default CommodityPrice
