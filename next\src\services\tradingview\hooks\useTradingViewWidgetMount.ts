'use client'

import { useEffect, useMemo, useRef } from 'react'

interface UseTradingViewWidgetMountProps {
  scriptSrc: string
  config: Record<string, any>
  dependencies?: any[]
}

/**
 * Custom hook for TradingView widgets that prevents infinite loops
 * and provides proper lifecycle management
 */
export function useTradingViewWidgetMount({
  scriptSrc,
  config,
  dependencies = [],
}: UseTradingViewWidgetMountProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const isInitializedRef = useRef(false)

  // Memoize the widget configuration to prevent unnecessary re-renders
  const memoizedConfig = useMemo(() => config, dependencies)

  useEffect(() => {
    if (!containerRef.current) return

    // Clear any existing content first
    containerRef.current.innerHTML = ''

    // Create and append the script
    const script = document.createElement('script')
    script.src = scriptSrc
    script.async = true
    script.innerHTML = JSON.stringify(memoizedConfig)

    containerRef.current.appendChild(script)
    isInitializedRef.current = true

    // Cleanup function
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
      isInitializedRef.current = false
    }
  }, [scriptSrc, memoizedConfig])

  return {
    containerRef,
    isInitialized: isInitializedRef.current,
  }
}
