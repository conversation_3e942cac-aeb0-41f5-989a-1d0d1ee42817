const siteUrl = process.env.NEXT_PUBLIC_URL || 'https://www.kitco.com'

/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl,
  generateRobotsTxt: true,
  sitemapSize: 50000, // Maximum value is 50,000
  generateIndexSitemap: true,
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
    ],
    additionalSitemaps: [
      `${siteUrl}/sitemap/dynamic.xml`,
      `${siteUrl}/static-sitemaps/news.xml`,
      `${siteUrl}/static-sitemaps/off-the-wire.xml`,
      `${siteUrl}/static-sitemaps/opinions.xml`,
      `${siteUrl}/static-sitemaps/video.xml`,
      `${siteUrl}/static-sitemaps/symbols-index.xml`,
    ],
  },
  exclude: [
    '/_next/*',
    '/admin/*',
    '/auth/*',
    '/api/*',
    '/charts',
    '/discourse/*',
    '/login',
    '/login/*',
    '/maintenance/*',
    '/news/category/*',
    '/opinion',
    '/robots.txt',
    '/sitemap/*',
    '/static/*',
    '/markets/futures',
    '/markets/futures/*',
  ],
}
