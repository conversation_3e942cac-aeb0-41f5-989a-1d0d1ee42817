import type { FC } from 'react'
import { useEffect, useRef } from 'react'

interface CompanyProfileWidgetProps {
  symbol: string
  width?: string | number
  height?: string | number
  isTransparent?: boolean
  colorTheme?: 'light' | 'dark'
  locale?: string
}

export const CompanyProfileWidget: FC<CompanyProfileWidgetProps> = ({
  symbol,
  width = '100%',
  height = '100%',
  isTransparent = false,
  colorTheme = 'light',
  locale = 'en',
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const widgetId = useRef(`tradingview-widget-${Date.now()}-${Math.random()}`)

  useEffect(() => {
    if (!containerRef.current || !symbol) return

    const container = containerRef.current

    // Clear any existing content first to prevent widget accumulation
    container.innerHTML = `
      <div class="tradingview-widget-container__widget"></div>
      <div class="tradingview-widget-copyright">
        <a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank">
          <span class="blue-text">Track all markets on TradingView</span>
        </a>
      </div>
    `

    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-symbol-profile.js'
    script.async = true
    script.innerHTML = JSON.stringify({
      width,
      height,
      isTransparent,
      colorTheme,
      symbol,
      locale,
      container_id: widgetId.current,
      largeChartUrl: `${process.env.NEXT_PUBLIC_URL}/markets/stocks/${symbol}`,
    })

    container.appendChild(script)

    return () => {
      // Clear all content including any TradingView generated elements
      if (container) {
        container.innerHTML = ''
      }
    }
  }, [symbol, width, height, isTransparent, colorTheme, locale])

  return (
    <div
      key={`${symbol}-${widgetId.current}`} // Force remount when symbol changes
      className="tradingview-widget-container"
      ref={containerRef}
      id={widgetId.current}
      style={{ width, height }}
    />
  )
}
