import { elasticClient, isElasticAvailable } from '../client/elasticsearch'
import { TradingViewSymbol } from '../types/TradingViewSymbol'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Get a symbol from Elasticsearch
 * @param symbol - The symbol to retrieve
 * @returns Promise that resolves to the symbol data or null if not found
 */
export async function getSymbolFromElastic(
  symbol: string,
): Promise<TradingViewSymbol | null> {
  // Return null if Elasticsearch is not available
  if (!isElasticAvailable() || !elasticClient) {
    console.log(
      `[getSymbolFromElastic] Elasticsearch not available, skipping fetch for symbol: ${symbol}`,
    )
    return null
  }

  try {
    console.log(`[getSymbolFromElastic] Searching for symbol: ${symbol}`)

    // Try multiple query approaches to match the search API behavior
    // First, try exact term match on symbol-fullname.keyword
    let result = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1,
      query: {
        term: {
          'symbol-fullname.keyword': symbol,
        },
      },
    })

    if (result.hits.hits.length > 0) {
      console.log(`[getSymbolFromElastic] Found ${symbol} via exact term match`)
      return result.hits.hits[0]._source as TradingViewSymbol
    }

    // If not found, try match query on symbol-fullname (like search API)
    result = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1,
      query: {
        match: {
          'symbol-fullname': symbol,
        },
      },
    })

    if (result.hits.hits.length > 0) {
      console.log(`[getSymbolFromElastic] Found ${symbol} via match query`)
      return result.hits.hits[0]._source as TradingViewSymbol
    }

    // If still not found, try simple symbol field match
    result = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1,
      query: {
        term: {
          'symbol.keyword': symbol,
        },
      },
    })

    if (result.hits.hits.length > 0) {
      console.log(
        `[getSymbolFromElastic] Found ${symbol} via symbol field match`,
      )
      return result.hits.hits[0]._source as TradingViewSymbol
    }

    console.log(
      `[getSymbolFromElastic] Symbol ${symbol} not found in Elasticsearch`,
    )
    return null
  } catch (error) {
    console.error(
      `[getSymbolFromElastic] ElasticSearch error for symbol ${symbol}:`,
      error,
    )
    return null
  }
}
