import type { FC } from 'react'
import SubFilterItem from '~/src/components/DataTable/SubFilter/SubFilterItem'

interface DataTableSubFilterProps {
  onFilterChange: (filter: string) => void
  activeFilter: string
  filters: string[]
  uppercase?: boolean
}

/**
 * Sub filter component for the data table
 *
 * @param onFilterChange
 * @param activeFilter
 * @param filters
 * @param uppercase
 * @constructor
 */
const DataTableSubFilter: FC<DataTableSubFilterProps> = ({
  onFilterChange,
  activeFilter,
  filters,
  uppercase = true,
}: DataTableSubFilterProps) => {
  return (
    <div className="inline-flex w-full items-end justify-start gap-24 self-stretch rounded-xl border border-slate-200">
      <div className="flex shrink grow basis-0 items-end justify-start self-stretch">
        <SubFilterItem
          name="ALL"
          onSelect={() => onFilterChange('ALL')}
          selected={activeFilter === 'ALL'}
          first={true}
        />
        {filters.map((filter) => (
          <SubFilterItem
            key={filter}
            name={uppercase ? filter.toUpperCase() : filter}
            onSelect={() => onFilterChange(filter)}
            selected={activeFilter === filter}
            last={filter === filters[filters.length - 1]}
          />
        ))}
      </div>
    </div>
  )
}

export default DataTableSubFilter
