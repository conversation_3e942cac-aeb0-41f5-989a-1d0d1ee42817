import { useEffect, useState } from 'react'

interface ScriptLoaderOptions {
  src: string
  async?: boolean
}

/**
 * Hook for loading external scripts
 */
export function useScriptLoader({
  src,
  async = true,
}: ScriptLoaderOptions): boolean {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const script = document.createElement('script')
    script.src = src
    script.async = async
    script.onload = () => {
      setIsLoaded(true)
    }

    document.head.appendChild(script)

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script)
      }
    }
  }, [src, async])

  return isLoaded
}
