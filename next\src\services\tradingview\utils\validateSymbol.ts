/**
 * Validate a symbol client-side by calling the API
 */
export async function validateSymbol(symbol: string): Promise<boolean> {
  try {
    // Basic validation - symbols should only contain letters, numbers, dots, hyphens, colons, underscores, and slashes
    const isValidFormat = /^[A-Za-z0-9\._\-:\/]+$/.test(symbol)
    if (!isValidFormat) {
      return false
    }

    // Call our API to verify the symbol
    const response = await fetch(
      `/api/symbol/verify?symbol=${encodeURIComponent(symbol)}`,
    )

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }

    const data = await response.json()
    return data.exists
  } catch (error) {
    console.error('Error validating symbol:', error)
    return false
  }
}
