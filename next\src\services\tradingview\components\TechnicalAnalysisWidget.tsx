import { useEffect, useRef } from 'react'

interface TechnicalAnalysisWidgetProps {
  symbol: string
  interval?: string
  colorTheme?: 'light' | 'dark'
  displayMode?: 'single' | 'multiple'
  isTransparent?: boolean
  locale?: string
  showIntervalTabs?: boolean
  width?: string | number
  height?: string | number
}

export const TechnicalAnalysisWidget: React.FC<
  TechnicalAnalysisWidgetProps
> = ({
  symbol,
  interval = '1D',
  colorTheme = 'light',
  displayMode = 'single',
  isTransparent = false,
  locale = 'en',
  showIntervalTabs = true,
  width = '100%',
  height = '100%',
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const widgetId = useRef(
    `tradingview-technical-${Date.now()}-${Math.random()}`,
  )

  useEffect(() => {
    if (!containerRef.current || !symbol) return

    const container = containerRef.current

    // Clear any existing content first to prevent widget accumulation
    container.innerHTML = ''

    const script = document.createElement('script')
    script.src =
      'https://s3.tradingview.com/external-embedding/embed-widget-technical-analysis.js'
    script.async = true
    script.innerHTML = JSON.stringify({
      interval,
      width,
      isTransparent,
      height,
      symbol,
      showIntervalTabs,
      displayMode,
      locale,
      colorTheme,
      container_id: widgetId.current,
      largeChartUrl: `${process.env.NEXT_PUBLIC_URL}/markets/stocks/${symbol}`,
    })

    container.appendChild(script)

    return () => {
      // Clear all content including any TradingView generated elements
      if (container) {
        container.innerHTML = ''
      }
    }
  }, [
    symbol,
    interval,
    colorTheme,
    displayMode,
    isTransparent,
    locale,
    showIntervalTabs,
    width,
    height,
  ])

  return (
    <div
      key={`${symbol}-${widgetId.current}`} // Force remount when symbol changes
      className="tradingview-widget-container"
      ref={containerRef}
      id={widgetId.current}
      style={{ width, height }}
    />
  )
}
