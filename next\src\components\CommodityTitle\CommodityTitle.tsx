import { Socials } from '~/src/components/socials/Socials'
import { CurrencySelect } from '../CurrencySelect'
import styles from './CommodityTitle.module.scss'

interface Props {
  commodity: string
}

const CommodityTitle = ({ commodity }: Props) => {
  return (
    <div>
      <h1 className="text-4xl font-bold capitalize">{commodity} Price Today</h1>
      <div className={styles.mainTitleFlexSb}>
        <CurrencySelect classNamesListbox={styles.listbox} />
        <Socials
          email={''}
          facebook={'kitco'}
          linkedIn={'kitco'}
          twitter={'kitco'}
        />
      </div>
    </div>
  )
}

export default CommodityTitle
