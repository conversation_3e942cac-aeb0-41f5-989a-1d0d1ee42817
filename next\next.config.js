/** @type {import('next').NextConfig} */
const nextConfig = {
  productionBrowserSourceMaps:
    process.env.NEXT_PUBLIC_ENABLE_DEV_MAPS === 'true',
  images: {
    remotePatterns: [
      { protocol: 'https', hostname: '**.kitco.com' },
      {
        protocol: 'https',
        hostname: '**.favish.com',
      },
      { protocol: 'https', hostname: '**.googleapis.com' },
      {
        protocol: 'https',
        hostname: '**.favish.workers.dev',
      },
      { protocol: 'https', hostname: '**.cryptocompare.com' },
      { protocol: 'https', hostname: '**.xsgames.co' }, // dev and test images
      { protocol: 'https', hostname: '**.placeholder.com' },
      { protocol: 'https', hostname: '**.weblinks247.com' },
    ],
  },
  async redirects() {
    return [
      {
        source: '/charts/livegold:path*',
        destination: '/charts/gold',
        permanent: true,
      },
      {
        source: '/charts/liveplatinum:path*',
        destination: '/charts/platinum',
        permanent: true,
      },
      {
        source: '/charts/historicalpalladium:path*',
        destination: '/charts/palladium',
        permanent: true,
      },
      {
        source: '/charts/liverhodium:path*',
        destination: '/charts/rhodium',
        permanent: true,
      },
      {
        source: '/rve',
        destination: 'https://kitco.simplybook.me/v2/',
        permanent: true,
      },
      {
        source: '/charts/livesilver:path*',
        destination: '/charts/silver',
        permanent: true,
      },
      {
        source: '/ads.txt',
        destination:
          'https://us-central1-favish-general.cloudfunctions.net/ads-txt-controller?domain=kitco.com',
        permanent: false,
        basePath: false,
      },
      {
        source: '/markets/stocks',
        destination: '/markets',
        permanent: false,
        missing: [
          {
            type: 'query',
            key: 'tvwidgetsymbol',
          },
        ],
      },
    ]
  },
  headers: async () => {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'max-age=60, s-maxage=60, stale-while-revalidate=60',
          },
        ],
      },
    ]
  },
}

module.exports = nextConfig
