import { elasticClient } from '../client/elasticsearch'
import { TradingViewSymbol } from '../types/TradingViewSymbol'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Fuzzy search symbols by symbol, description, or symbol-fullname.
 * Returns up to 'limit' results (default 10).
 */
export async function fuzzySearchSymbols(
  query: string,
  limit = 10,
): Promise<TradingViewSymbol[]> {
  try {
    const response = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: limit,
      query: {
        multi_match: {
          query: query,
          fields: ['symbol', 'description', 'symbol-fullname'],
          fuzziness: 'AUTO',
        },
      },
    })

    return response.hits.hits.map((hit) => hit._source as TradingViewSymbol)
  } catch (error) {
    console.error('Error fuzzy searching symbols in Elasticsearch:', error)
    return []
  }
}
