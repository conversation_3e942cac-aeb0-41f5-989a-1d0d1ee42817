/**
 * Parameters for the Kitco proxy API request
 */
export interface KitcoApiRequestParams {
  /** The symbol to fetch data for (e.g., 'XAU', 'HUI', 'SPX') */
  symbol: string
  /** API version, defaults to '2.0' */
  version?: string
  /** Response type, defaults to 'xml' */
  type?: 'xml'
}

/**
 * Built URL parameters for the Kitco proxy API
 */
export interface KitcoApiUrlParams {
  ver: string
  symbol: string
  type: string
}
