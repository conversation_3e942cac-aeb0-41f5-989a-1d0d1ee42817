import { clsx } from 'clsx'
import { useRouter } from 'next/router'
import type React from 'react'
import { useEffect, useRef, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { ErrBoundary } from '../ErrBoundary/ErrBoundary'
import Footer from '../Footer/Footer'
import Header from '../Header/Header'
import NavVer2 from '../NavVer2/NavVer2'
import styles from './Layout.module.scss'

/**
 * Props interface for the Layout component
 * @interface Props
 */
interface Props {
  /**
   * React children elements to be rendered within the main content area
   * @type {React.ReactNode}
   * @optional
   */
  children?: React.ReactNode

  /**
   * Page title to be passed to the Header component
   * @type {string}
   */
  title: string

  /**
   * Whether to use wide layout styling for the main content area
   * @type {boolean}
   * @default false
   */
  wide?: boolean
}

/**
 * Pathname that should not be included in print output
 * @constant {string}
 */
const PATHNAME_NO_PRINT = '/price/precious-metals/text-quotes'

/**
 * Main layout component that provides the overall page structure including header, navigation,
 * main content area, and footer. Handles responsive behavior, mobile menu states, and
 * advertisement placement.
 *
 * @component
 * @param {Props} props - The component props
 * @param {React.ReactNode} [props.children] - Content to render in the main area
 * @param {string} props.title - Page title for the header
 * @param {boolean} [props.wide=false] - Whether to use wide layout styling
 * @returns {JSX.Element} The rendered layout component
 *
 * @example
 * ```tsx
 * <Layout title="Home Page" wide={false}>
 *   <div>Page content goes here</div>
 * </Layout>
 * ```
 */
const Layout = ({ children, title, wide = false }: Props) => {
  const router = useRouter()

  /**
   * State to track navigation visibility/hiding behavior
   * @type {boolean | null}
   */
  const [hidden, setHidden] = useState<boolean | null>(null)

  /**
   * State to track mobile menu open/closed state
   * @type {boolean}
   */
  const [isMobileMenu, setIsMobileMenu] = useState<boolean>(false)

  /**
   * Reference to the advertisement container element for height calculations
   * @type {React.RefObject<HTMLDivElement>}
   */
  const heightAds = useRef<HTMLDivElement>(null)

  /**
   * Reference to the navigation container element for height and position calculations
   * @type {React.RefObject<HTMLDivElement>}
   */
  const heightNav = useRef<HTMLDivElement>(null)

  /**
   * Y position of the navigation element relative to the viewport
   * @type {number | undefined}
   */
  const navPositionY = heightNav.current?.getBoundingClientRect().y

  /**
   * State to track current window height for mobile menu calculations
   * @type {number}
   */
  const [windowH, setWindowH] = useState<number>(0)

  /**
   * Effect to handle window resize events and update height calculations
   * for mobile menu positioning
   */
  useEffect(() => {
    /**
     * Updates the window height state based on current viewport and navigation position
     */
    const updateHeight = () => {
      if (heightNav.current) {
        setWindowH(window.innerHeight - navPositionY)
      }
    }

    updateHeight()

    window.addEventListener('resize', updateHeight)
    return () => {
      window.removeEventListener('resize', updateHeight)
    }
  }, [heightNav.current, isMobileMenu])

  return (
    <>
      <Header title={title} />
      <div className="py-5 bg-white no-print" ref={heightAds}>
        <AdvertisingSlot
          id={'leaderboard'}
          className={clsx(
            'h-[100px] w-[320px] md:h-[90px] md:w-[728px] lg:w-[970px] lg:h-[250px]',
            'mx-auto',
            'flex items-center justify-center no-print',
          )}
        />
      </div>
      <div
        ref={heightNav}
        className={clsx(
          'z-[999]',
          isMobileMenu
            ? 'sticky overflow-auto z-[999999999] bg-black/50'
            : 'bg-white',
          hidden || hidden === null ? '' : 'sticky top-0',
          router.pathname === PATHNAME_NO_PRINT ? 'no-print' : '',
        )}
        style={{ height: isMobileMenu ? `${`${windowH}px`}` : 'auto' }}
      >
        <NavVer2
          onShowHide={setHidden}
          onMenuMobile={setIsMobileMenu}
          headerRef={{
            heightHeader: Number(
              heightAds.current?.clientHeight + heightNav.current?.clientHeight,
            ),
            heightAds: Number(heightAds.current?.clientHeight),
          }}
          wide={wide}
        />
      </div>
      <div
        className={router.pathname === PATHNAME_NO_PRINT ? 'no-print' : ''}
        style={{ position: 'relative', height: '20px' }}
      />
      <main
        className={wide ? styles.mainAppWrapperWide : styles.mainAppWrapper}
      >
        <ErrBoundary>{children}</ErrBoundary>
      </main>
      <div className={router.pathname === PATHNAME_NO_PRINT ? 'no-print' : ''}>
        <Footer wide={wide} />
      </div>
    </>
  )
}

export default Layout
