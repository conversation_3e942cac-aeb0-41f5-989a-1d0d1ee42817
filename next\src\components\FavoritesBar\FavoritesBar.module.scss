@use './../../styles/vars' as *;

.background {
  background-color: $light-grey;
  width: 100%;
  height: 40px;
  overflow-x: auto;

  &::-webkit-scrollbar {
    display: none;
  }
}

nav.wrapper {
  height: 36px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  & ul {
    padding: 8px 0;
    display: flex;
    align-items: center;
    list-style-type: none;

    & li:last-of-type {
      border-right: 0;
    }

    & li {
      border-right: solid 1px black;
      display: flex;
      align-items: center;

      & a {
        height: 100%;
        padding-left: 13px;
        padding-right: 13px;
        font-size: 12px;
        color: #373737;
        text-decoration: none;
      }
    }
  }

  @media screen and (max-width: 768px) {
    display: none;
  }
}

.refreshContainer {
  display: flex;
  align-items: center;
  height: 100%;
  cursor: pointer;
  background-color: transparent;

  span {
    margin-bottom: 2px;
  }
}
