import dynamic from 'next/dynamic'
import Script from 'next/script'
import { useEffect, useState } from 'react'
import Zoom from 'react-medium-image-zoom'
import MetalQuoteCell from '~/src/components-metals/MetalQuoteCell/MetalQuoteCell'
import BuySellButton from '~/src/components/BuySellButton/BuySellButton'
import NewsSidebar from '~/src/components/Commodity/DetailPageChart/NewsSidebar'
import { TimestampProvider } from '~/src/utils/ctxTimestamp'
import useScreenSize from '~/src/utils/useScreenSize'
import { ErrBoundary } from '../../ErrBoundary/ErrBoundary'
import { MiniLabel } from '../../MiniLabel/MiniLabel'
import { TradingViewChartWidget } from '../../TradingViewChartWidget/TradingViewChartWidget'

interface AdvertisingSlotProps {
  id: string
  className?: string
}

// Dynamic Import with Explicit Typing
const AdvertisingSlot = dynamic(
  () => import('react-advertising').then((mod) => mod.AdvertisingSlot),
  {
    ssr: false,
  },
) as React.FC<AdvertisingSlotProps>

/**
 * Detail Page Chart Props
 */
interface DetailPageChartProps {
  name: string
  symbol: string
  ssrTimestamp: number
}

/**
 * Mapping for metal names
 */
const NAME_MAPPING = {
  gold: 'gold',
  silver: 'silver',
  platinum: 'plati',
  palladium: 'plad',
  rhodium: 'rhodium',
}

const DetailPageChart = ({
  name,
  symbol,
  ssrTimestamp,
}: DetailPageChartProps) => {
  // Image Name State
  const [nameImg, setNameImg] = useState('gold')

  // Show NY Chart State
  const [showNYChart, setShowNYChart] = useState(false)

  // Screen Size
  const { isTablet, isDesktop } = useScreenSize()

  let windowWidth = null

  if (typeof window !== 'undefined') {
    windowWidth = window.innerWidth
  }

  useEffect(() => {
    setNameImg(NAME_MAPPING[name] || 'gold')
    setShowNYChart(name === 'gold' || name === 'silver')
  }, [name])

  /**
   * Get Image
   * @param name - Name of the metal (e.g. gold, silver, etc.)
   * @param symbol - Symbol of the metal (e.g. XAU, XAG, etc.)
   */
  const getImage = (name: string, symbol: string) => {
    let imageNames = ['0030lnb', '0060lnb', '0182nyb', '0365nyb', '1825nyb']

    if (name !== 'platinum' && name !== 'palladium') {
      imageNames.push('3650nyb')
    }

    if (name === 'rhodium') {
      imageNames = ['0030lnb', '0060lnb', '0182lnb', '0365lnb', '1825lnb']
    }

    return imageNames.map((imageName) => (
      <span
        key={imageName}
        className="w-[calc(50%_-_(15px_/2))] min-w-[calc(305px)]"
      >
        {!isTablet && !isDesktop ? (
          <img
            className="w-[calc(50%_-_(15px_/2))] min-w-[calc(305px)]"
            key={imageName}
            alt={`Live ${name} chart`}
            src={`${process.env.NEXT_PUBLIC_URL}/chart-images/LFgif/${symbol}${imageName}.gif`}
          />
        ) : (
          <Zoom
            key={imageName}
            classDialog="custom-zoom"
            zoomMargin={50}
            wrapElement="span"
          >
            <img
              className="w-[calc(50%_-_(15px_/2))] min-w-[calc(305px)]"
              key={imageName}
              alt={`Live ${name} chart`}
              src={`${process.env.NEXT_PUBLIC_URL}/chart-images/LFgif/${symbol}${imageName}.gif`}
            />
          </Zoom>
        )}
      </span>
    ))
  }

  return (
    <>
      <MiniLabel name={name} />
      <div
        className="block md:gap-[15px]
            tablet:grid tablet:grid-cols-[300px_1fr] tablet:grid-rows-[auto_auto]
            desktop:grid-cols-[300px_1fr_300px]"
      >
        <div className="tablet:col-start-1 tablet:row-start-1">
          <div className="relative mb-[30px] rounded-lg border border-[#E5E5E5] px-[15px] pb-[17px] pt-[10px] leading-5">
            <TimestampProvider timestamp={ssrTimestamp}>
              <ErrBoundary>
                <MetalQuoteCell symbol={symbol} />
              </ErrBoundary>
            </TimestampProvider>
            <BuySellButton
              width={240}
              containerClassName="pt-4 flex items-center justify-center"
              buttonClassName="text-[14px]"
            />
          </div>
          {windowWidth <= 767 && (
            <AdvertisingSlot
              id={'banner-1'}
              className="mx-auto flex justify-center items-center my-[15px] h-[280px] w-[336px]
                  tablet:h-[90px] tablet:w-[728px]"
            />
          )}
          {windowWidth >= 767 && (
            <AdvertisingSlot
              id={'right-rail-2'}
              className="mx-auto h-[600px] w-[300px] sticky top-4"
            />
          )}
        </div>
        <div className="tablet:col-start-2 tablet:row-span-3">
          {name !== 'rhodium' && (
            <div className="relative mb-3 h-[500px] ">
              <TradingViewChartWidget symbol={`X${symbol}USD`} />
            </div>
          )}
          {name !== 'rhodium' && (
            <>
              <div
                className="dianomi_context mt-4 min-h-[702px] w-full tablet:min-h-[400px]"
                data-dianomi-context-id="246"
              />
              <Script
                src="https://www.dianomi.com/js/contextfeed.js"
                id="dianomi_context_script"
              />
            </>
          )}
          <div className="mb-[15px]">
            {name !== 'rhodium' &&
              (!isTablet && !isDesktop ? (
                <img
                  id={`chart_live_${name}`}
                  src={`${process.env.NEXT_PUBLIC_URL}/chart-images/images/live/${nameImg}.gif`}
                  alt={`Live 24hrs ${name} chart`}
                />
              ) : (
                <Zoom
                  key={`chart_live_${name}`}
                  classDialog="custom-zoom"
                  zoomMargin={50}
                >
                  <img
                    id={`chart_live_${name}`}
                    src={`${process.env.NEXT_PUBLIC_URL}/chart-images/images/live/${nameImg}.gif`}
                    alt={`Live 24hrs ${name} chart`}
                  />
                </Zoom>
              ))}
          </div>
          {name !== 'rhodium' &&
            showNYChart &&
            (!isTablet && !isDesktop ? (
              <div className="mb-[15px]">
                <img
                  id={`chart_ny_${name}`}
                  src={`${process.env.NEXT_PUBLIC_URL}/chart-images/images/live/ny${nameImg}.gif`}
                  alt={`Live New York ${name} Chart`}
                />
              </div>
            ) : (
              <div className="mb-[15px]">
                <Zoom
                  key={`chart_ny_${name}`}
                  classDialog="custom-zoom"
                  zoomMargin={50}
                >
                  <img
                    id={`chart_ny_${name}`}
                    src={`${process.env.NEXT_PUBLIC_URL}/chart-images/images/live/ny${nameImg}.gif`}
                    alt={`Live New York ${name} Chart`}
                  />
                </Zoom>
              </div>
            ))}
          {name !== 'rhodium' && windowWidth <= 767 && (
            <AdvertisingSlot
              id={'banner-2'}
              className="mx-auto flex justify-center items-center my-[15px] h-[280px] w-[336px]
              tablet:h-[90px] tablet:w-[728px]"
            />
          )}
          {NAME_MAPPING[name] === 'gold' && (
            <BuySellButton
              width={240}
              containerClassName="pt-4 pb-5 flex items-center justify-center text-[14px]"
            />
          )}
          <div className="flex flex-wrap justify-center gap-[15px]">
            {getImage(name, symbol)}
          </div>
        </div>
        <div
          className="mb-[30px]
              tablet:col-start-1 tablet:row-start-3
              desktop:col-start-3 desktop:row-start-1"
        >
          <NewsSidebar name={name} />
        </div>
      </div>
    </>
  )
}

export default DetailPageChart
