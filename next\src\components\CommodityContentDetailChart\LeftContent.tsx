import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import MetalQuoteCell from '~/src/components-metals/MetalQuoteCell/MetalQuoteCell'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { TimestampProvider } from '~/src/utils/ctxTimestamp'

interface LeftContentProps {
  name: string
  symbol: string
  ssrTimestamp: number
  isBaseMetal?: boolean
}

const LeftContent: FC<LeftContentProps> = ({
  symbol,
  ssrTimestamp,
  isBaseMetal,
}: LeftContentProps) => {
  return (
    <div className="w-full">
      <div className="relative mb-[30px] rounded-lg border border-[#E5E5E5] px-[15px] pb-[17px] leading-5">
        <TimestampProvider timestamp={ssrTimestamp}>
          <ErrBoundary>
            <MetalQuoteCell symbol={symbol} isBaseMetal={isBaseMetal} />
          </ErrBoundary>
        </TimestampProvider>
      </div>

      <AdvertisingSlot
        id={'right-rail-3'}
        className={'mx-auto hidden h-[600px] w-[300px] md:block'}
      />
    </div>
  )
}

export default LeftContent
