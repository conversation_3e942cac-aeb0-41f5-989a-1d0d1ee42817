@import './vars';

.gridTwoColumn {
  display: grid;
  grid-template-columns: 1fr 360px;
  column-gap: 40px;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    column-gap: 0;
  }
}

.chartBlock {
  margin: 4em 0;

  @media (max-width: 768px) {
    margin: 2em 0;
  }

  // Ensure chart container meets desktop requirements and scales on mobile
  .tradingview-widget-container {
    width: 100%;
    max-width: 800px;
    height: 468px;

    @media (max-width: 768px) {
      height: 400px; // Responsive height for mobile
      max-width: 100%; // Scale to fit mobile screen
    }

    @media (max-width: 480px) {
      height: 350px; // Further reduced for small screens
    }
  }
}

.infoBlock {
  margin: 2em 0;

  @media (max-width: 768px) {
    margin: 1.5em 0;
  }
}

// Responsive widget containers
.symbolInfoWidget,
.companyProfileWidget,
.technicalAnalysisWidget {
  width: 100%;
  max-width: 100%;

  @media (min-width: 1024px) {
    max-width: 300px;
  }
}

// Mobile-specific ad adjustments
.mobileAdContainer {
  @media (max-width: 768px) {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
}

// Responsive text scaling
.responsiveTitle {
  @media (max-width: 768px) {
    font-size: 1.5rem;
  }

  @media (max-width: 480px) {
    font-size: 1.25rem;
  }
}

// Attribution section mobile adjustments
.attributionMobile {
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;

    p {
      font-size: 0.75rem;
      line-height: 1.3;
    }
  }
}
