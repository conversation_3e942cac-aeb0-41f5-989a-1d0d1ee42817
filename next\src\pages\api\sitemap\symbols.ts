import type { NextApiRequest, NextApiResponse } from 'next'
import { generateSymbolsSitemaps } from '~/src/features/sitemaps/symbolsGenerator'

/**
 * Generate and store the symbols sitemaps.
 *
 * @param req
 * @param res
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Start the symbols sitemap generation process
    console.log('Starting symbols sitemap generation via API...')

    // Run in background - don't await to avoid timeout
    generateSymbolsSitemaps()
      .then(() => {
        console.log('Symbols sitemaps generation completed successfully')
      })
      .catch((error) => {
        console.error('Symbols sitemaps generation failed:', error)
      })

    res.status(202).json({
      message:
        'Symbols sitemap generation started. This process may take several minutes due to the large dataset.',
    })
  } catch (error) {
    console.error('Failed to start symbols sitemap generation:', error)
    res
      .status(500)
      .json({ error: 'Failed to start symbols sitemap generation' })
  }
}
