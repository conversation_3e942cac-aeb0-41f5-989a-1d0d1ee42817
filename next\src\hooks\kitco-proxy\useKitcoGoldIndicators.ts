import { useQuery } from '@tanstack/react-query'
import { fetchGoldIndicatorsData } from '~/src/services/kitco-proxy/api/fetchGoldIndicatorsData'
import type { KitcoValue } from '~/src/services/kitco-proxy/types/KitcoValue'

interface KitcoGoldIndicatorsData {
  indicators: KitcoValue[]
  xau: KitcoValue | null
  hui: KitcoValue | null
  spttgd: KitcoValue | null
  timestamp: string | null
}

/**
 * Custom hook to fetch gold indicators data from Kitco proxy using the multi-symbol format
 * URL: https://proxy.kitco.com/getValue?ver=2.0&symbol=XAU,HUI,SPTTGD&type=xml
 * Format: XAU2025-07-30 14:00:00209.20\-1.93\-0.91 HUI2025-07-30 14:00:00431.83\-4.79\-1.10 SPTTGD2025-07-30 14:00:00513.36\-4.54\-0.88
 */
export function useKitcoGoldIndicators() {
  return useQuery<KitcoGoldIndicatorsData>({
    queryKey: ['kitco-gold-indicators'],
    queryFn: async () => {
      const indicators = await fetchGoldIndicatorsData()

      // Extract individual symbols
      const xau = indicators.find((item) => item.symbol === 'XAU') || null
      const hui = indicators.find((item) => item.symbol === 'HUI') || null
      const spttgd = indicators.find((item) => item.symbol === 'SPTTGD') || null

      // Use the timestamp from the first available symbol
      const timestamp =
        xau?.timestamp || hui?.timestamp || spttgd?.timestamp || null

      return {
        indicators,
        xau,
        hui,
        spttgd,
        timestamp,
      }
    },
    staleTime: 30000, // Data is fresh for 30 seconds
    refetchInterval: 60000, // Refetch every minute
    refetchOnWindowFocus: true,
    retry: 3,
  })
}
