import type { KitcoValue } from '../types/KitcoValue'
import { parseXmlResponse } from '../utils/parseXmlResponse'

/**
 * Gold ratios symbols for calculating ratios (Gold Bid / [commodity or Index])
 */
export const GOLD_RATIOS_SYMBOLS = ['XAU', 'HUI', 'SPX', 'DJI', 'CL'] as const

/**
 * Fetches gold ratios data from Kitco proxy
 * URL: https://proxy.kitco.com/getValue?ver=2.0&symbol=XAU,HUI,SPX,DJI,CL&type=xml
 *
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to array of gold ratios data
 */
export async function fetchGoldRatiosTableData(
  version = '2.0',
): Promise<KitcoValue[]> {
  try {
    // Build the Kitco proxy URL directly
    const symbols = GOLD_RATIOS_SYMBOLS.join(',')
    const url = `https://proxy.kitco.com/getValue?ver=${version}&symbol=${symbols}&type=xml`

    // Fetch data directly from Kitco proxy
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'text/plain, text/xml',
        'User-Agent': 'Kitco-CMS-Next/1.0',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`)
    }

    // Get the XML response as text
    const responseText = await response.text()
    console.log('Raw response from Kitco proxy (gold ratios):', responseText)

    if (!responseText) {
      throw new Error('Empty response from API')
    }

    // Parse the XML response
    const parsedResponse = await parseXmlResponse(responseText)
    console.log('Parsed gold ratios data:', parsedResponse.values)

    if (!parsedResponse.values || parsedResponse.values.length === 0) {
      throw new Error('Failed to parse any data from XML response')
    }

    // Filter to only the symbols we want
    const goldRatiosData = parsedResponse.values.filter((value) =>
      GOLD_RATIOS_SYMBOLS.includes(value.symbol as any),
    )

    if (goldRatiosData.length === 0) {
      throw new Error('No matching symbols found in response')
    }

    return goldRatiosData
  } catch (error) {
    console.error('Error fetching gold ratios data:', error)
    console.error(
      'Kitco URL attempted:',
      `https://proxy.kitco.com/getValue?ver=${version}&symbol=${GOLD_RATIOS_SYMBOLS.join(',')}&type=xml`,
    )

    // More detailed error logging
    if (
      error instanceof TypeError &&
      error.message.includes('Failed to fetch')
    ) {
      throw new Error('Network error - unable to reach Kitco proxy')
    } else if (error instanceof Error) {
      throw new Error(`API Error: ${error.message}`)
    } else {
      throw new Error('Unknown error occurred while fetching data')
    }
  }
}

/**
 * Fetches a specific symbol from the gold ratios data
 *
 * @param symbol - Symbol to fetch (XAU, HUI, SPX, DJI, or CL)
 * @param version - API version (optional, defaults to '2.0')
 * @returns Promise resolving to specific symbol data or null if not found
 */
export async function fetchGoldRatiosSymbol(
  symbol: 'XAU' | 'HUI' | 'SPX' | 'DJI' | 'CL',
  version = '2.0',
): Promise<KitcoValue | null> {
  const allData = await fetchGoldRatiosTableData(version)
  return allData.find((item) => item.symbol === symbol) || null
}
