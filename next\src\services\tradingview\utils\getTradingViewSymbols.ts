import { elasticClient } from '../client/elasticsearch'
import { TradingViewSymbol } from '../types/TradingViewSymbol'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

let symbolsCache: TradingViewSymbol[] | null = null

/**
 * Loads and caches TradingView symbols from Elasticsearch.
 * @param size - Number of symbols to load (default: 1000)
 */
export async function getTradingViewSymbols(
  size = 1000,
): Promise<TradingViewSymbol[]> {
  if (symbolsCache) return symbolsCache

  try {
    const response = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: size,
      _source: true,
    })

    symbolsCache = response.hits.hits.map(
      (hit) => hit._source as TradingViewSymbol,
    )
    return symbolsCache
  } catch (error) {
    console.error('Error loading symbols from Elasticsearch:', error)
    return []
  }
}
