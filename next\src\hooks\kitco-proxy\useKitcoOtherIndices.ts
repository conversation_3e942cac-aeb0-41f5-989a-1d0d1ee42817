import { useQuery } from '@tanstack/react-query'
import { fetchOtherIndicesData } from '~/src/services/kitco-proxy/api/fetchOtherIndicesData'
import type { KitcoValue } from '~/src/services/kitco-proxy/types/KitcoValue'

interface KitcoOtherIndicesData {
  indices: KitcoValue[]
  dji: KitcoValue | null
  ixic: KitcoValue | null
  spx: KitcoValue | null
  nya: KitcoValue | null
  n225: KitcoValue | null
  gsptse: KitcoValue | null
  timestamp: string | null
}

export function useKitcoOtherIndices() {
  return useQuery<KitcoOtherIndicesData>({
    queryKey: ['kitco-other-indices'],
    queryFn: async () => {
      const indices = await fetchOtherIndicesData()

      const dji = indices.find((item) => item.symbol === 'DJI') || null
      const ixic = indices.find((item) => item.symbol === 'IXIC') || null
      const spx = indices.find((item) => item.symbol === 'SPX') || null
      const nya = indices.find((item) => item.symbol === 'NYA') || null
      const n225 = indices.find((item) => item.symbol === 'N225') || null
      const gsptse = indices.find((item) => item.symbol === 'GSPTSE') || null

      // Use the timestamp from the first available symbol
      const timestamp =
        dji?.timestamp ||
        ixic?.timestamp ||
        spx?.timestamp ||
        nya?.timestamp ||
        n225?.timestamp ||
        gsptse?.timestamp ||
        null

      return {
        indices,
        dji,
        ixic,
        spx,
        nya,
        n225,
        gsptse,
        timestamp,
      }
    },
    staleTime: 30000,
    refetchInterval: 60000,
    refetchOnWindowFocus: true,
    retry: 3,
  })
}
