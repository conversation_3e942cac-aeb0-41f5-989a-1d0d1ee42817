import { useQuery } from '@tanstack/react-query'
import { fetchXauDataCompact } from '~/src/services/kitco-proxy/api/fetchXauData'
import type { KitcoValue } from '~/src/services/kitco-proxy/types/KitcoValue'

interface KitcoXauData {
  xau: KitcoValue | null
  timestamp: string | null
}

/**
 * Custom hook to fetch XAU data from Kitco proxy using the compact format
 * URL: https://proxy.kitco.com/getValue?ver=2.0&symbol=XAU&type=xml
 * Format: XAU2025-07-30 13:37:00208.62\-2.51\-1.19
 */
export function useKitcoXau() {
  return useQuery<KitcoXauData>({
    queryKey: ['kitco-xau-compact'],
    queryFn: async () => {
      const xau = await fetchXauDataCompact()

      return {
        xau,
        timestamp: xau?.timestamp || null,
      }
    },
    staleTime: 30000, // Data is fresh for 30 seconds
    refetchInterval: 60000, // Refetch every minute
    refetchOnWindowFocus: true,
    retry: 3,
  })
}
