import classNames from 'classnames'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { env } from '~/src/env/client.mjs'
import styles from './FeedbackForm.module.scss'

interface FormInput {
  name: string
  email: string
  subject: string
  message: string
}

const FeedbackForm = () => {
  const {
    handleSubmit,
    register,
    formState: { errors },
    reset,
  } = useForm<FormInput>()

  const [message, setMessage] = useState<string>(null)

  const encryptLong = async (str: string) => {
    const JSEncrypt = (await import('jsencrypt')).default
    const encryptor = new JSEncrypt()
    encryptor.setPublicKey(env.NEXT_PUBLIC_KEY_ENCRYPT)

    const maxChunkLength = 100
    let output = ''
    let inOffset = 0

    while (inOffset < str.length) {
      output += encryptor.encrypt(
        str.substring(inOffset, inOffset + maxChunkLength),
      )
      inOffset += maxChunkLength
    }
    return output
  }

  const onSubmit = async (data: FormInput) => {
    const stringToEncrypt = JSON.stringify(data)
    const encryptedData = await encryptLong(stringToEncrypt)

    try {
      const response = await fetch(
        `${env.NEXT_PUBLIC_URL_CLOUDFLARE}/send_feedback`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ data: encryptedData }),
        },
      )

      if (response.ok) {
        setMessage('Thank you for the feedback!')
        setTimeout(() => {
          reset()
          setMessage('')
        }, 2000)
      } else {
        throw new Error('Network response was not ok')
      }
    } catch {
      setMessage('Sorry. An error occurred.')
      setTimeout(() => {
        setMessage('')
      }, 2000)
    }
  }

  const nameClassNames = classNames(styles.input, styles.textfield, {
    [styles.error]: errors.name,
  })

  const emailClassNames = classNames(styles.input, styles.textfield, {
    [styles.error]: errors.email,
  })

  const subjectClassNames = classNames(styles.input, styles.textfield, {
    [styles.error]: errors.subject,
  })

  const messageClassNames = classNames(styles.input, styles.textfield, {
    [styles.error]: errors.message,
  })

  return (
    <form className={styles.form} onSubmit={handleSubmit(onSubmit)}>
      <div className={styles.header}>
        <svg
          className={styles.icon}
          width="321"
          height="321"
          viewBox="0 0 321 321"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M288.231 0.333313H32.2307C14.6307 0.333313 0.390713 14.7333 0.390713 32.3333L0.230713 320.333L64.2307 256.333H288.231C305.831 256.333 320.231 241.933 320.231 224.333V32.3333C320.231 14.7333 305.831 0.333313 288.231 0.333313ZM256.231 192.333H64.2307V160.333H256.231V192.333ZM256.231 144.333H64.2307V112.333H256.231V144.333ZM256.231 96.3333H64.2307V64.3333H256.231V96.3333Z"
            fill="#E5B53A"
          />
        </svg>
        <p className={styles.title}>We appreciate your feedback</p>
      </div>
      <input
        className={nameClassNames}
        placeholder="Name"
        {...register('name', { required: true })}
      />
      <input
        className={emailClassNames}
        placeholder="Email"
        type="email"
        {...register('email', { required: true })}
      />
      <input
        className={subjectClassNames}
        placeholder="Subject"
        {...register('subject', { required: true })}
      />
      <textarea
        className={messageClassNames}
        placeholder="Message"
        rows={6}
        {...register('message', { required: true })}
      />
      <div className={styles.actions}>
        <button className={styles.submit} type="submit">
          Submit
        </button>
        {message && <div className={styles.message}>{message}</div>}
      </div>
    </form>
  )
}

export default FeedbackForm
