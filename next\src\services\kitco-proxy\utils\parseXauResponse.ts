import type { KitcoValue } from '../types/KitcoValue'

/**
 * Parses the compact XAU response format from Kitco proxy
 * Format: XAU2025-07-30 13:37:00208.62\-2.51\-1.19
 *
 * @param responseText - Raw response text from the API
 * @returns Parsed KitcoValue object or null if parsing fails
 */
export function parseXauResponse(responseText: string): KitcoValue | null {
  try {
    // Clean the response text
    const cleanText = responseText.trim()

    if (!cleanText.startsWith('XAU')) {
      throw new Error('Response does not start with XAU symbol')
    }

    // Remove the XAU prefix
    const dataText = cleanText.substring(3)

    // Split by backslash to get the parts
    const parts = dataText.split('\\')

    if (parts.length < 3) {
      throw new Error('Invalid response format - expected at least 3 parts')
    }

    // Extract timestamp and price from the first part
    // Format: 2025-07-30 13:37:00208.62
    const timestampAndPrice = parts[0]

    // Extract timestamp (first 19 characters: YYYY-MM-DD HH:MM:SS)
    const timestamp = timestampAndPrice.substring(0, 19)

    // Extract price (remaining characters)
    const priceText = timestampAndPrice.substring(19)
    const price = parseFloat(priceText)

    // Extract change and changePercentage
    const change = parseFloat(parts[1])
    const changePercentage = parseFloat(parts[2])

    // Validate parsed values
    if (isNaN(price) || isNaN(change) || isNaN(changePercentage)) {
      throw new Error('Failed to parse numeric values from response')
    }

    if (!timestamp || timestamp.length !== 19) {
      throw new Error('Invalid timestamp format')
    }

    return {
      symbol: 'XAU',
      timestamp,
      price,
      change,
      changePercentage,
    }
  } catch (error) {
    console.error('Error parsing XAU response:', error)
    console.error('Raw response:', responseText)
    return null
  }
}

/**
 * Validates that the response text looks like a valid XAU response
 *
 * @param responseText - Raw response text to validate
 * @returns True if the response looks valid
 */
export function isValidXauResponse(responseText: string): boolean {
  return responseText.trim().startsWith('XAU') && responseText.includes('\\')
}
