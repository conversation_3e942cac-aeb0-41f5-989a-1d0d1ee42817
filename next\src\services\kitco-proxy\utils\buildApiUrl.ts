import type { KitcoApiRequestParams } from '../types/KitcoApiRequest'

/**
 * Base URL for the Kitco proxy API
 */
const KITCO_PROXY_BASE_URL = 'https://proxy.kitco.com/getValue'

/**
 * Builds the complete API URL for fetching data from Kitco proxy
 *
 * @param params - Request parameters
 * @returns Complete API URL
 */
export function buildApiUrl(params: KitcoApiRequestParams): string {
  const urlParams = {
    ver: params.version || '2.0',
    symbol: params.symbol,
    type: params.type || 'xml',
  }

  const searchParams = new URLSearchParams(urlParams)
  return `${KITCO_PROXY_BASE_URL}?${searchParams.toString()}`
}

/**
 * Validates the symbol parameter
 *
 * @param symbol - Symbol to validate
 * @returns True if valid
 */
export function isValidSymbol(symbol: string): boolean {
  // Basic validation - symbol should be a non-empty string with alphanumeric characters
  return /^[A-Z0-9]+$/i.test(symbol.trim())
}
