import { useQuery } from '@tanstack/react-query'
import { fetchGoldRatiosTableData } from '~/src/services/kitco-proxy/api/fetchGoldRatiosTableData'
import type { KitcoValue } from '~/src/services/kitco-proxy/types/KitcoValue'

interface GoldRatiosCalculated {
  silver?: number
  platinum?: number
  palladium?: number
  xau?: number
  hui?: number
  spx?: number
  dowi?: number
  crudeOil?: number
}

export interface KitcoGoldRatiosTableData {
  values: KitcoValue[]
  xau: KitcoValue | null
  hui: KitcoValue | null
  spx: KitcoValue | null
  dji: KitcoValue | null
  cl: KitcoValue | null
  ratios: GoldRatiosCalculated
  timestamp: string | null
}

/**
 * React Query hook to fetch gold ratios data from Kitco proxy
 * Calculates ratios using formula: Gold Bid / [commodity or Index]
 */
export function useKitcoGoldRatiosTable() {
  return useQuery<KitcoGoldRatiosTableData>({
    queryKey: ['kitco-gold-ratios-table'],
    queryFn: async () => {
      const values = await fetchGoldRatiosTableData()

      // Extract individual symbols
      const xau = values.find((item) => item.symbol === 'XAU') || null
      const hui = values.find((item) => item.symbol === 'HUI') || null
      const spx = values.find((item) => item.symbol === 'SPX') || null
      const dji = values.find((item) => item.symbol === 'DJI') || null
      const cl = values.find((item) => item.symbol === 'CL') || null

      // Use the timestamp from the first available symbol (usually XAU)
      const timestamp =
        xau?.timestamp ||
        hui?.timestamp ||
        spx?.timestamp ||
        dji?.timestamp ||
        cl?.timestamp ||
        null

      // Note: For proper ratio calculations, we need the actual gold bid price as numerator
      // This hook only provides prices for denominators (XAU, HUI, SPX, DJI, CL)
      // The actual gold bid price should come from the existing data source
      // So we return the raw prices here and let the component do the ratio calculation

      const ratios: GoldRatiosCalculated = {
        // Note: For metals like silver, platinum, palladium we would need additional data
        // Ratios will be calculated in the component using proper gold bid as numerator
        xau: undefined, // Will be calculated with proper gold bid in component
        hui: undefined, // Will be calculated with proper gold bid in component
        spx: undefined, // Will be calculated with proper gold bid in component
        dowi: undefined, // Will be calculated with proper gold bid in component
        crudeOil: undefined, // Will be calculated with proper gold bid in component
      }

      return {
        values,
        xau,
        hui,
        spx,
        dji,
        cl,
        ratios,
        timestamp,
      }
    },
    staleTime: 30000,
    refetchInterval: 60000,
    refetchOnWindowFocus: true,
    retry: 3,
  })
}
