import type { FC } from 'react'
import Percentage from '~/src/components/Price/Percentage'
import Price from '~/src/components/Price/Price'
import type ChangeData from '~/src/types/DataTable/ChangeData'
import type MiningEquity from '~/src/types/DataTable/MiningEquity'

/**
 * PriceChange props
 *
 * @property {ChangeData | MiningEquity} value - The value of the change
 * @property {string} className - The class name for the component
 * @property {string} changeKey - The key for the change (numeric)
 * @property {string} changeValKey - The key for the change value (numeric)
 * @property {string} percentageKey - The key for the percentage (numeric)
 * @property {string} percentageValKey - The key for the percentage value (numeric)
 * @property {string} symbol - The symbol of the currency
 * @property {ChangeData | MiningEquity} value - The value of the change
 */
interface PriceChangeProps {
  className?: string
  changeKey?: string
  changeValKey?: string
  percentageKey?: string
  percentageValKey?: string
  symbol?: string
  value: ChangeData | MiningEquity
}

const PriceChange: FC<PriceChangeProps> = ({
  className = 'flex grow basis-0 items-center justify-start gap-2',
  changeKey = 'change',
  changeValKey = 'changeVal',
  percentageKey = 'percentage',
  percentageValKey = 'percentageVal',
  symbol = '$',
  value,
}: PriceChangeProps) => {
  return (
    <div className={className}>
      <div className="flex w-20 items-start">
        <Price
          price={value?.[changeKey]}
          priceVal={value?.[changeValKey]}
          symbol={symbol}
        />
      </div>
      <div className="bg-emerald-200/opacity-25 flex items-center justify-start rounded-3xl px-1 pb-1 pt-0.5 pr-0">
        <Percentage
          percentage={value?.[percentageKey]}
          percentageVal={value?.[percentageValKey]}
        />
      </div>
    </div>
  )
}

export default PriceChange
