'use client'

import { useEffect, useState } from 'react'
import { TradingViewSymbol } from '../types/TradingViewSymbol'
import { getSymbolFromElastic } from '../utils/getSymbolFromElastic'
import { isValidTradingViewSymbol } from '../utils/isValidTradingViewSymbol'

interface UseTradingViewSymbolResult {
  isValid: boolean
  isValidating: boolean
  symbolData: TradingViewSymbol | null
  error: string | null
}

/**
 * Hook for validating TradingView symbols
 * @param symbol - The symbol to validate
 * @returns Validation state and symbol data
 */
export function useTradingViewSymbol(
  symbol: string,
): UseTradingViewSymbolResult {
  const [isValid, setIsValid] = useState<boolean>(false)
  const [isValidating, setIsValidating] = useState<boolean>(true)
  const [symbolData, setSymbolData] = useState<TradingViewSymbol | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let isMounted = true

    const validateSymbol = async () => {
      if (!symbol) {
        if (isMounted) {
          setIsValid(false)
          setIsValidating(false)
          setError('No symbol provided')
        }
        return
      }

      setIsValidating(true)
      setError(null)

      try {
        // Check if the symbol is valid
        const valid = await isValidTradingViewSymbol(symbol)

        if (isMounted) {
          setIsValid(valid)

          if (valid) {
            // Try to get the symbol data
            const data = await getSymbolFromElastic(symbol)
            if (isMounted) {
              setSymbolData(data)
            }
          } else {
            setError(`Invalid TradingView symbol: ${symbol}`)
          }
        }
      } catch (err) {
        if (isMounted) {
          setIsValid(false)
          setError(
            `Error validating symbol: ${err instanceof Error ? err.message : String(err)}`,
          )
        }
      } finally {
        if (isMounted) {
          setIsValidating(false)
        }
      }
    }

    validateSymbol()

    return () => {
      isMounted = false
    }
  }, [symbol])

  return { isValid, isValidating, symbolData, error }
}
