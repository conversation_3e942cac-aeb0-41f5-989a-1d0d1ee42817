import { elasticClient, isElasticAvailable } from '../client/elasticsearch'

// Index where symbols are stored
const SYMBOLS_INDEX = process.env.ELASTICSEARCH_SYMBOLS_INDEX || 'symbols'

/**
 * Check if a symbol exists in Elasticsearch
 * @param symbol - The symbol to check
 * @returns Promise that resolves to true if symbol exists, false otherwise
 */
export async function symbolExistsInElastic(symbol: string): Promise<boolean> {
  // Return false if Elasticsearch is not available
  if (!isElasticAvailable() || !elasticClient) {
    console.log(
      `[symbolExistsInElastic] Elasticsearch not available, skipping check for symbol: ${symbol}`,
    )
    return false
  }

  try {
    console.log(`[symbolExistsInElastic] Searching for symbol: ${symbol}`)

    // Try multiple query approaches to match the search API behavior
    // First, try exact term match on symbol-fullname.keyword
    let result = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1,
      query: {
        term: {
          'symbol-fullname.keyword': symbol,
        },
      },
    })

    let exists =
      result.hits.total && typeof result.hits.total === 'object'
        ? result.hits.total.value > 0
        : (result.hits.total as number) > 0

    if (exists) {
      console.log(
        `[symbolExistsInElastic] Found ${symbol} via exact term match`,
      )
      return true
    }

    // If not found, try match query on symbol-fullname (like search API)
    result = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1,
      query: {
        match: {
          'symbol-fullname': symbol,
        },
      },
    })

    exists =
      result.hits.total && typeof result.hits.total === 'object'
        ? result.hits.total.value > 0
        : (result.hits.total as number) > 0

    if (exists) {
      console.log(`[symbolExistsInElastic] Found ${symbol} via match query`)
      return true
    }

    // If still not found, try simple symbol field match
    result = await elasticClient.search({
      index: SYMBOLS_INDEX,
      size: 1,
      query: {
        term: {
          'symbol.keyword': symbol,
        },
      },
    })

    exists =
      result.hits.total && typeof result.hits.total === 'object'
        ? result.hits.total.value > 0
        : (result.hits.total as number) > 0

    console.log(
      `[symbolExistsInElastic] Result for ${symbol}: ${exists} (total hits: ${JSON.stringify(result.hits.total)})`,
    )
    return exists
  } catch (error) {
    console.error(
      `[symbolExistsInElastic] ElasticSearch error for symbol ${symbol}:`,
      error,
    )

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as any).message || ''
      if (
        errorMessage.includes('ECONNREFUSED') ||
        errorMessage.includes('ConnectionError')
      ) {
        console.error(
          `[symbolExistsInElastic] Elasticsearch connection failed - service may be down`,
        )
      }
    }

    return false
  }
}
