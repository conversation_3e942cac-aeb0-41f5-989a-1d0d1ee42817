import {
  type Cell,
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  type TableOptions,
  useReactTable,
} from '@tanstack/react-table'
import clsx from 'clsx'
import { useEffect, useMemo, useState } from 'react'
import { FaCaretDown, FaCaretUp } from 'react-icons/fa'
import { FaSort } from 'react-icons/fa6'
import DataTablePagination from '~/src/components/DataTable/Pagination/DataTablePagination'
import { getCommonPinningStyles } from '~/src/components/DataTable/Styles/DataTablePinningStyles'

interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  extraConfig?: Partial<TableOptions<T>>
  isLoading?: boolean
  scrollOnDesktop?: boolean
  paginationEnabled?: boolean
  paginationClassName?: string
}

const DataTable = <T extends {}>({
  data,
  columns,
  extraConfig,
  scrollOnDesktop = false,
  paginationEnabled = false,
  paginationClassName = '',
  isLoading,
}: DataTableProps<T>) => {
  /**
   * Table configuration with extra config from the props
   */
  const tableConfig = useMemo(() => {
    // Default table configuration
    const tableDefault: TableOptions<T> = {
      data,
      columns,
      getCoreRowModel: getCoreRowModel<T>(),
      columnResizeMode: 'onChange',
    }

    // If the extra config is an object, merge it with the default config
    return typeof extraConfig === 'object'
      ? {
          ...tableDefault,
          ...extraConfig,
        }
      : tableDefault
  }, [data, columns, extraConfig])

  // Table instance
  const table = useReactTable(tableConfig)

  // Selected row state
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null)

  /**
   * Handle the row click
   *
   * @param rowId
   */
  const handleRowClick = (rowId: string) => {
    setSelectedRowId(rowId)
  }

  /**
   * Update the page size when the extra config changes (mobile pagination)
   */
  useEffect(() => {
    if (tableConfig?.initialState?.pagination?.pageSize) {
      table.setPageSize(tableConfig.initialState.pagination.pageSize)
    }
  }, [extraConfig])

  /**
   * Render the cell
   *
   * @param cell
   */
  const renderCell = (cell: Cell<T, unknown>) => {
    return flexRender(cell.column.columnDef.cell, {
      ...cell.getContext(),
    })
  }

  if (!data || isLoading) {
    return <div>Loading...</div>
  }

  return (
    <div
      className={clsx(
        'w-full overflow-x-auto',
        scrollOnDesktop ? '' : 'lg:overflow-x-hidden',
      )}
    >
      <table className={`table-auto w-full`}>
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const { column } = header
                const headerMeta: any = header.column.columnDef.meta
                const headerClassName = headerMeta?.classNameHeader

                return (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    // IMPORTANT: For setting the sticky styles
                    style={{ ...getCommonPinningStyles(column) }}
                    className={clsx(
                      'h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 text-left',
                      headerClassName,
                    )}
                  >
                    <div
                      className={clsx(
                        'flex cursor-pointer select-none items-center gap-2 text-xs font-bold leading-tight text-zinc-600',
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none'
                          : '',
                        headerMeta?.classNameHeaderDiv,
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                      onKeyDown={header.column.getToggleSortingHandler()}

                      // Title disabled for avoid conflict with the tooltip
                      /*
  title={
    header.column.getCanSort()
      ? header.column.getNextSortingOrder() === 'asc'
        ? 'Sort ascending'
        : header.column.getNextSortingOrder() === 'desc'
          ? 'Sort descending'
          : 'Clear sort'
      : undefined
  }
  */
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}{' '}
                      {({
                        asc: <FaCaretUp />,
                        desc: <FaCaretDown />,
                      }[header.column.getIsSorted() as string] ??
                      tableConfig.enableSorting) ? (
                        <FaSort />
                      ) : null}
                    </div>
                  </th>
                )
              })}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr
              key={row.id}
              className={clsx(
                'border-b border-slate-200 hover:bg-[#F8F8F8]',
                row.id === selectedRowId ? 'bg-[#EFEFEF]' : 'bg-white',
              )}
              onClick={() => handleRowClick(row.id)}
              onKeyDown={() => handleRowClick(row.id)}
            >
              {row.getVisibleCells().map((cell) => {
                const { column } = cell

                const headerMeta: any = cell.column.columnDef.meta
                const cellClassName = headerMeta?.classNameCell

                return (
                  <td
                    key={cell.id}
                    // IMPORTANT: For setting the sticky styles
                    style={{ ...getCommonPinningStyles(column) }}
                    className={clsx('px-2', cellClassName)}
                  >
                    {renderCell(cell)}
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>
      {paginationEnabled && (
        <div className={paginationClassName}>
          <DataTablePagination
            table={table}
            setPageSize={table.setPageSize}
            setPageIndex={table.setPageIndex}
          />
        </div>
      )}
    </div>
  )
}

export default DataTable
