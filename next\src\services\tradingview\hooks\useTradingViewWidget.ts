'use client'

import { useEffect, useState } from 'react'
import { TradingViewWidgetProps } from '../types/TradingViewWidget'
import { validateSymbol } from '../utils/validateSymbol'

interface UseTradingViewWidgetParams extends TradingViewWidgetProps {
  containerId?: string
}

/**
 * Custom hook for initializing TradingView widgets
 * @param params Configuration for the TradingView widget
 * @returns Widget state and handlers
 */
export function useTradingViewWidget(params: UseTradingViewWidgetParams) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [isValidSymbol, setIsValidSymbol] = useState<boolean>(true)

  // Generate a unique container ID if not provided
  const containerId =
    params.containerId ||
    params.container_id ||
    `tradingview_${Math.random().toString(36).substr(2, 9)}`

  useEffect(() => {
    const validateAndInitialize = async () => {
      // Validate symbol if provided
      if (params.symbol) {
        try {
          const symbolIsValid = await validateSymbol(params.symbol)
          setIsValidSymbol(symbolIsValid)

          if (!symbolIsValid) {
            setHasError(true)
            setErrorMessage(`Invalid symbol: ${params.symbol}`)
            setIsLoading(false)
            params.onError?.(`Invalid symbol: ${params.symbol}`)
            return
          }
        } catch (error) {
          setIsValidSymbol(false)
          setHasError(true)
          setErrorMessage(`Symbol validation error: ${error}`)
          setIsLoading(false)
          params.onError?.(`Symbol validation error: ${error}`)
          return
        }
      }

      // Create configuration object excluding container-related props
      const config = {
        symbol: params.symbol,
        onError: params.onError,
        theme: params.theme,
        autosize: params.autosize,
        height: params.height,
        width: params.width,
        interval: params.interval,
        timezone: params.timezone,
        style: params.style,
        locale: params.locale,
        toolbar_bg: params.toolbar_bg,
        enable_publishing: params.enable_publishing,
        withdateranges: params.withdateranges,
        hide_side_toolbar: params.hide_side_toolbar,
        allow_symbol_change: params.allow_symbol_change,
        save_image: params.save_image,
        show_popup_button: params.show_popup_button,
        popup_width: params.popup_width,
        popup_height: params.popup_height,
        largeChartUrl: params.largeChartUrl,
      }

      // Initialize the widget
      const initWidget = () => {
        try {
          // Clean up existing widget if any
          const container = document.getElementById(containerId)
          if (container) {
            container.innerHTML = ''
          }

          // Create new widget script
          const script = document.createElement('script')
          script.type = 'text/javascript'
          script.src = 'https://s3.tradingview.com/tv.js'
          script.async = true
          script.onload = () => {
            if (window.TradingView) {
              new window.TradingView.widget({
                ...config,
                container_id: containerId,
              })
              setIsLoading(false)
              setHasError(false)
            }
          }
          script.onerror = () => {
            setHasError(true)
            setErrorMessage('Failed to load TradingView widget')
            setIsLoading(false)
            params.onError?.('Failed to load TradingView widget')
          }

          document.head.appendChild(script)
        } catch (error) {
          setHasError(true)
          setErrorMessage(`Widget initialization error: ${error}`)
          setIsLoading(false)
          params.onError?.(`Widget initialization error: ${error}`)
        }
      }

      // Check if TradingView is already loaded
      if (window.TradingView) {
        try {
          new window.TradingView.widget({
            ...config,
            container_id: containerId,
          })
          setIsLoading(false)
          setHasError(false)
        } catch (error) {
          setHasError(true)
          setErrorMessage(`Widget creation error: ${error}`)
          setIsLoading(false)
          params.onError?.(`Widget creation error: ${error}`)
        }
      } else {
        initWidget()
      }
    }

    validateAndInitialize()

    // Cleanup function
    return () => {
      const container = document.getElementById(containerId)
      if (container) {
        container.innerHTML = ''
      }
    }
  }, [params.symbol, containerId])

  return {
    isLoading,
    hasError,
    errorMessage,
    isValidSymbol,
    containerId,
  }
}
