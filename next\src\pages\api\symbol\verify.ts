import type { NextApiRequest, NextApiResponse } from 'next'
import { getSymbolFromElastic } from '~/src/services/tradingview/utils/getSymbolFromElastic'
import { symbolExistsInElastic } from '~/src/services/tradingview/utils/symbolExistsInElastic'

type ResponseData = {
  exists: boolean
  symbolData?: any
  error?: string
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData>,
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ exists: false, error: 'Method not allowed' })
  }

  const { symbol } = req.query

  if (!symbol || typeof symbol !== 'string') {
    return res.status(400).json({ exists: false, error: 'Symbol is required' })
  }

  try {
    // Check if symbol exists in Elasticsearch
    const exists = await symbolExistsInElastic(symbol)

    if (exists) {
      // Get full symbol data if it exists
      const symbolData = await getSymbolFromElastic(symbol)
      return res.status(200).json({ exists, symbolData })
    }

    return res.status(200).json({ exists })
  } catch (error) {
    console.error('Error verifying symbol:', error)
    return res
      .status(500)
      .json({ exists: false, error: 'Internal server error' })
  }
}
