import type { NextApiRequest, NextApiResponse } from 'next'
import { generateSymbolsSitemaps } from '~/src/features/sitemaps/symbolsGenerator'
import { StoreSitemap } from '~/src/features/sitemaps/utils'

/**
 * Generate and store the sitemaps.
 *
 * @param req
 * @param res
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Start the sitemap generation process in background
    generateSitemaps()
      .then(() => {
        console.log('All sitemaps generated and uploaded successfully.')
      })
      .catch((error) => {
        console.error('Failed to generate sitemaps:', error)
      })

    res.status(202).json({ message: 'Sitemap generation in progress.' })
  } catch (error) {
    console.error('Failed to start sitemap generation:', error)
    res.status(500).json({ error: 'Failed to start sitemap generation' })
  }
}

/**
 * Generate and upload sitemaps for the website
 */
async function generateSitemaps() {
  try {
    await StoreSitemap('getAllNewsArticles', 'news.xml')
    await StoreSitemap('getAllOffTheWire', 'off-the-wire.xml')
    await StoreSitemap('getAllOpinions', 'opinions.xml')
    await StoreSitemap('video', 'video.xml')

    // Generate symbols sitemaps with batching
    await generateSymbolsSitemaps()

    console.log('Sitemaps generated and uploaded successfully.')
  } catch (error) {
    console.error('Failed to generate or upload sitemaps:', error)
  }
}
